#!/usr/bin/env node

/**
 * AI Chat Setup Script
 * 
 * This script helps integrate the AI chat module into your existing project.
 * Run with: node setup-ai-chat.js
 */

const fs = require('fs');
const path = require('path');

console.log('🤖 AI Chat Integration Setup');
console.log('============================\n');

// Check if we're in the right directory
const currentDir = process.cwd();
const backendPath = path.join(currentDir, 'apps', 'backend');
const frontendPath = path.join(currentDir, 'apps', 'frontend');

if (!fs.existsSync(backendPath) || !fs.existsSync(frontendPath)) {
  console.error('❌ Error: This script must be run from the project root directory.');
  console.error('   Make sure you have apps/backend and apps/frontend directories.');
  process.exit(1);
}

console.log('✅ Project structure verified');

// Check backend app.module.ts
const appModulePath = path.join(backendPath, 'src', 'app', 'app.module.ts');
if (fs.existsSync(appModulePath)) {
  const appModuleContent = fs.readFileSync(appModulePath, 'utf8');
  
  if (appModuleContent.includes('AiChatModule')) {
    console.log('✅ AI Chat module already imported in app.module.ts');
  } else {
    console.log('⚠️  AI Chat module not found in app.module.ts');
    console.log('   Please manually add the import as shown in the integration guide.');
  }
} else {
  console.log('❌ app.module.ts not found');
}

// Check environment variables
const envPath = path.join(backendPath, '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  if (envContent.includes('OPENAI_API_KEY')) {
    console.log('✅ OpenAI API key found in .env');
  } else {
    console.log('⚠️  OpenAI API key not found in .env');
    console.log('   Please add: OPENAI_API_KEY=your-api-key-here');
  }
  
  if (envContent.includes('FRONTEND_URL')) {
    console.log('✅ Frontend URL configured in .env');
  } else {
    console.log('⚠️  Frontend URL not found in .env');
    console.log('   Please add: FRONTEND_URL=http://localhost:3000');
  }
} else {
  console.log('❌ .env file not found in backend');
}

// Check frontend environment
const frontendEnvPath = path.join(frontendPath, '.env.local');
if (fs.existsSync(frontendEnvPath)) {
  const frontendEnvContent = fs.readFileSync(frontendEnvPath, 'utf8');
  
  if (frontendEnvContent.includes('NEXT_PUBLIC_API_URL')) {
    console.log('✅ API URL configured in frontend .env.local');
  } else {
    console.log('⚠️  API URL not found in frontend .env.local');
    console.log('   Please add: NEXT_PUBLIC_API_URL=http://localhost:3001');
  }
} else {
  console.log('⚠️  .env.local not found in frontend');
  console.log('   Creating .env.local with default configuration...');
  
  const defaultEnvContent = 'NEXT_PUBLIC_API_URL=http://localhost:3001\n';
  fs.writeFileSync(frontendEnvPath, defaultEnvContent);
  console.log('✅ Created .env.local with default API URL');
}

// Check if AI chat files exist
const aiChatModulePath = path.join(backendPath, 'src', 'ai-chat', 'ai-chat.module.ts');
const chatInterfacePath = path.join(frontendPath, 'components', 'ai-chat', 'ChatInterface.tsx');

if (fs.existsSync(aiChatModulePath)) {
  console.log('✅ Backend AI chat module files found');
} else {
  console.log('❌ Backend AI chat module files not found');
  console.log('   Please ensure all files from apps/backend/src/ai-chat/ are present');
}

if (fs.existsSync(chatInterfacePath)) {
  console.log('✅ Frontend AI chat component files found');
} else {
  console.log('❌ Frontend AI chat component files not found');
  console.log('   Please ensure all files from apps/frontend/components/ai-chat/ are present');
}

// Check package.json dependencies
const backendPackagePath = path.join(backendPath, 'package.json');
const frontendPackagePath = path.join(frontendPath, 'package.json');

if (fs.existsSync(backendPackagePath)) {
  const backendPackage = JSON.parse(fs.readFileSync(backendPackagePath, 'utf8'));
  const requiredBackendDeps = ['openai', 'socket.io', '@nestjs/websockets'];
  
  const missingBackendDeps = requiredBackendDeps.filter(dep => 
    !backendPackage.dependencies?.[dep] && !backendPackage.devDependencies?.[dep]
  );
  
  if (missingBackendDeps.length === 0) {
    console.log('✅ All required backend dependencies found');
  } else {
    console.log('⚠️  Missing backend dependencies:', missingBackendDeps.join(', '));
    console.log('   Run: npm install', missingBackendDeps.join(' '));
  }
}

if (fs.existsSync(frontendPackagePath)) {
  const frontendPackage = JSON.parse(fs.readFileSync(frontendPackagePath, 'utf8'));
  const requiredFrontendDeps = ['@chatscope/chat-ui-kit-react', 'socket.io-client'];
  
  const missingFrontendDeps = requiredFrontendDeps.filter(dep => 
    !frontendPackage.dependencies?.[dep] && !frontendPackage.devDependencies?.[dep]
  );
  
  if (missingFrontendDeps.length === 0) {
    console.log('✅ All required frontend dependencies found');
  } else {
    console.log('⚠️  Missing frontend dependencies:', missingFrontendDeps.join(', '));
    console.log('   Run: npm install', missingFrontendDeps.join(' '));
  }
}

console.log('\n🎉 Setup check complete!');
console.log('\nNext steps:');
console.log('1. Review the AI_CHAT_INTEGRATION_GUIDE.md for detailed instructions');
console.log('2. Start your backend: cd apps/backend && npm run start:dev');
console.log('3. Start your frontend: cd apps/frontend && npm run dev');
console.log('4. Visit http://localhost:3000/chat to test the AI chat');
console.log('5. Check the floating chat widget on other pages');

console.log('\n📚 Documentation:');
console.log('- Backend: apps/backend/src/ai-chat/README.md');
console.log('- Frontend: apps/frontend/components/ai-chat/README.md');
console.log('- Integration: AI_CHAT_INTEGRATION_GUIDE.md');

console.log('\n🔧 Troubleshooting:');
console.log('- Check console logs for errors');
console.log('- Verify OpenAI API key is valid');
console.log('- Test health endpoint: curl http://localhost:3001/ai-chat/health');
