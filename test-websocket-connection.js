#!/usr/bin/env node

/**
 * WebSocket Connection Test Script
 * Tests the AI chat WebSocket connection independently
 */

const { io } = require('socket.io-client');

console.log('🧪 Testing WebSocket Connection...\n');

// Test configuration
const BACKEND_URL = 'http://localhost:3001';
const NAMESPACE = '/ai-chat';
const TEST_MESSAGE = 'Hello, this is a test message';

// Create socket connection
console.log(`📡 Connecting to: ${BACKEND_URL}${NAMESPACE}`);

const socket = io(`${BACKEND_URL}${NAMESPACE}`, {
  transports: ['websocket', 'polling'],
  timeout: 10000,
  forceNew: true,
});

// Connection events
socket.on('connect', () => {
  console.log('✅ Connected successfully!');
  console.log(`   Socket ID: ${socket.id}`);
  
  // Test sending a message
  setTimeout(() => {
    console.log('\n📤 Sending test message...');
    socket.emit('sendMessage', {
      message: TEST_MESSAGE,
      userId: 'test-user',
      conversationId: 'test-conversation',
    });
  }, 1000);
});

socket.on('disconnect', () => {
  console.log('❌ Disconnected from server');
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error.message);
  process.exit(1);
});

// Chat events
socket.on('connected', (data) => {
  console.log('🎉 Chat service ready:', data);
});

socket.on('messageResponse', (response) => {
  console.log('📥 Received AI response:');
  console.log(`   Message: ${response.message}`);
  console.log(`   Source: ${response.source}`);
  console.log(`   Timestamp: ${response.timestamp}`);
  
  // Test completed successfully
  console.log('\n✅ WebSocket test completed successfully!');
  socket.disconnect();
  process.exit(0);
});

socket.on('aiTyping', (data) => {
  console.log(`⌨️  AI typing: ${data.isTyping}`);
});

socket.on('messageError', (error) => {
  console.error('❌ Message error:', error);
  socket.disconnect();
  process.exit(1);
});

// Timeout after 30 seconds
setTimeout(() => {
  console.error('⏰ Test timeout - no response received');
  socket.disconnect();
  process.exit(1);
}, 30000);

console.log('⏳ Waiting for connection...');
