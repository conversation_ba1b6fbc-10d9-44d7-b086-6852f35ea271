#!/usr/bin/env node

/**
 * AI Chat Test Script
 * 
 * This script tests the AI chat functionality via REST API.
 * Run with: node test-ai-chat.js
 */

const http = require('http');

const API_BASE = 'http://localhost:3001';

console.log('🧪 AI Chat Test Suite');
console.log('=====================\n');

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test 1: Health Check
async function testHealthCheck() {
  console.log('1. Testing health check...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3001,
      path: '/ai-chat/health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200 && response.data.status === 'ok') {
      console.log('   ✅ Health check passed');
      console.log('   📊 OpenAI status:', response.data.services?.openai || 'unknown');
      return true;
    } else {
      console.log('   ❌ Health check failed');
      console.log('   📊 Response:', response);
      return false;
    }
  } catch (error) {
    console.log('   ❌ Health check error:', error.message);
    return false;
  }
}

// Test 2: Send Message
async function testSendMessage() {
  console.log('\n2. Testing message sending...');
  
  try {
    const testMessage = {
      message: 'Hello, this is a test message. Please respond with "Test successful".',
    };

    const response = await makeRequest({
      hostname: 'localhost',
      port: 3001,
      path: '/ai-chat/message',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    }, testMessage);

    if (response.status === 201 && response.data.message) {
      console.log('   ✅ Message sent successfully');
      console.log('   🤖 AI Response:', response.data.message.substring(0, 100) + '...');
      console.log('   🆔 Conversation ID:', response.data.conversationId);
      return response.data.conversationId;
    } else {
      console.log('   ❌ Message sending failed');
      console.log('   📊 Response:', response);
      return null;
    }
  } catch (error) {
    console.log('   ❌ Message sending error:', error.message);
    return null;
  }
}

// Test 3: Get Conversation History
async function testConversationHistory(conversationId) {
  if (!conversationId) {
    console.log('\n3. Skipping conversation history test (no conversation ID)');
    return false;
  }

  console.log('\n3. Testing conversation history...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3001,
      path: `/ai-chat/conversation/${conversationId}/history`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200 && response.data.messages) {
      console.log('   ✅ Conversation history retrieved');
      console.log('   📝 Message count:', response.data.messages.length);
      return true;
    } else {
      console.log('   ❌ Conversation history failed');
      console.log('   📊 Response:', response);
      return false;
    }
  } catch (error) {
    console.log('   ❌ Conversation history error:', error.message);
    return false;
  }
}

// Test 4: WebSocket Connection (basic check)
async function testWebSocketConnection() {
  console.log('\n4. Testing WebSocket endpoint availability...');
  
  try {
    // Just check if the WebSocket endpoint responds (not a full WebSocket test)
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3001,
      path: '/socket.io/',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Socket.IO typically returns a specific response for GET requests
    if (response.status === 400 || response.data.includes('socket.io')) {
      console.log('   ✅ WebSocket endpoint is available');
      return true;
    } else {
      console.log('   ⚠️  WebSocket endpoint response unexpected');
      return false;
    }
  } catch (error) {
    console.log('   ❌ WebSocket endpoint error:', error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('Starting AI Chat tests...\n');
  
  const results = {
    health: false,
    message: false,
    history: false,
    websocket: false,
  };

  // Test health check
  results.health = await testHealthCheck();
  
  if (!results.health) {
    console.log('\n❌ Health check failed. Make sure the backend is running.');
    console.log('   Start with: cd apps/backend && npm run start:dev');
    return;
  }

  // Test message sending
  const conversationId = await testSendMessage();
  results.message = !!conversationId;

  // Test conversation history
  results.history = await testConversationHistory(conversationId);

  // Test WebSocket endpoint
  results.websocket = await testWebSocketConnection();

  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  console.log('Health Check:', results.health ? '✅ PASS' : '❌ FAIL');
  console.log('Message Sending:', results.message ? '✅ PASS' : '❌ FAIL');
  console.log('Conversation History:', results.history ? '✅ PASS' : '❌ FAIL');
  console.log('WebSocket Endpoint:', results.websocket ? '✅ PASS' : '❌ FAIL');

  const passCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\nOverall: ${passCount}/${totalTests} tests passed`);

  if (passCount === totalTests) {
    console.log('\n🎉 All tests passed! Your AI chat is ready to use.');
    console.log('\nNext steps:');
    console.log('1. Start your frontend: cd apps/frontend && npm run dev');
    console.log('2. Visit http://localhost:3000/chat');
    console.log('3. Test the chat interface');
  } else {
    console.log('\n⚠️  Some tests failed. Check the integration guide for troubleshooting.');
  }
}

// Run the tests
runTests().catch(console.error);
