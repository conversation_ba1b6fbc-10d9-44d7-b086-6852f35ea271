# AI Chat Integration Guide

This guide provides step-by-step instructions to integrate the AI-powered chatbot into your existing NestJS + Next.js project.

## 📋 Prerequisites

Ensure you have the following packages installed:

### Backend (NestJS)
```bash
npm install openai socket.io @nestjs/websockets uuid
npm install --save-dev @types/uuid
```

### Frontend (Next.js)
```bash
npm install @chatscope/chat-ui-kit-react socket.io-client
```

## 🔧 Backend Integration

### 1. Add the AI Chat Module to App Module

Edit `apps/backend/src/app/app.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from '../auth/auth.module';
import { NursesModule } from '../nurses/nurses.module';
import { RequestsModule } from '../requests/requests.module';
import { AdminModule } from '../admin/admin.module';
import { DashboardModule } from '../dashboard/dashboard.module';
import { UserManagementModule } from '../user-management/user-management.module';
import { ReviewsModule } from '../reviews/reviews.module';
import { EmailModule } from '../email/email.module';
import { AiChatModule } from '../ai-chat/ai-chat.module'; // Add this import
import { configValidationSchema } from '../config/config.validation';
import { GlobalExceptionFilter } from '../common/filters/global-exception.filter';
import { ResponseInterceptor } from '../common/interceptors/response.interceptor';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: configValidationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
        retryWrites: true,
        w: 'majority',
      }),
      inject: [ConfigService],
    }),
    AuthModule,
    NursesModule,
    RequestsModule,
    AdminModule,
    DashboardModule,
    UserManagementModule,
    ReviewsModule,
    EmailModule,
    AiChatModule, // Add this line
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}
```

### 2. Verify Environment Variables

Ensure your `apps/backend/.env` file contains:

```env
# Existing variables...
OPENAI_API_KEY=sk-your-openai-api-key-here
FRONTEND_URL=http://localhost:3000
```

### 3. Test Backend Integration

Start your backend server:
```bash
cd apps/backend
npm run start:dev
```

Test the health endpoint:
```bash
curl http://localhost:3001/ai-chat/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "openai": "healthy"
  }
}
```

## 🎨 Frontend Integration

### 1. Add Environment Variables

Create or update `apps/frontend/.env.local`:

```env
NEXT_PUBLIC_API_URL=http://localhost:3001
```

### 2. Add Chat Context to Your App

Edit `apps/frontend/pages/_app.tsx`:

```typescript
import type { AppProps } from 'next/app';
import { AuthProvider } from '../context/AuthContext';
import { ChatProvider } from '../context/ChatContext'; // Add this import
import '../styles/globals.css';

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <AuthProvider>
      <ChatProvider> {/* Add this wrapper */}
        <Component {...pageProps} />
      </ChatProvider>
    </AuthProvider>
  );
}

export default MyApp;
```

### 3. Add Floating Chat Widget (Optional)

To add the floating chat widget to all pages, edit `apps/frontend/components/Layout.tsx`:

```typescript
import React from 'react';
import Navbar from './Navbar';
import Footer from './Footer';
import { FloatingChatWidget } from './ai-chat/FloatingChatWidget'; // Add this import

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
      <FloatingChatWidget /> {/* Add this component */}
    </div>
  );
};

export default Layout;
```

### 4. Add Navigation Link (Optional)

To add a link to the chat page in your navigation, edit `apps/frontend/components/Navbar.tsx`:

```typescript
// Add this to your navigation links
<Link href="/chat">
  <a className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
    AI Chat
  </a>
</Link>
```

## 🚀 Usage Examples

### 1. Full Page Chat
Users can access the chat at `/chat` route.

### 2. Floating Widget
The floating widget appears on all pages (if enabled in Layout).

### 3. Custom Integration
```typescript
import { ChatInterface } from '../components/ai-chat/ChatInterface';

// In a modal or custom component
<div className="w-96 h-96">
  <ChatInterface 
    className="h-full"
    onClose={() => setModalOpen(false)}
  />
</div>
```

## 🔒 Security Considerations

### Rate Limiting
The backend includes built-in rate limiting (10 requests per minute per client).

### API Key Security
- Never expose your OpenAI API key in frontend code
- Keep it in backend environment variables only
- Use proper environment variable validation

### CORS Configuration
The WebSocket gateway is configured to accept connections from your frontend URL only.

## 🧪 Testing

### Backend Tests
```bash
cd apps/backend
npm run test
```

### Frontend Tests
```bash
cd apps/frontend
npm run test
```

### Manual Testing
1. Start both backend and frontend servers
2. Navigate to `/chat`
3. Send a test message
4. Verify AI response is received
5. Test floating widget functionality

## 📊 Monitoring

### Health Checks
- Backend: `GET /ai-chat/health`
- WebSocket: Connection status in browser console

### Logging
- Backend logs are available in the console
- Frontend errors logged to browser console
- WebSocket connection events are logged

## 🔧 Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check if backend is running on correct port
   - Verify CORS configuration
   - Check firewall settings

2. **OpenAI API Errors**
   - Verify API key is correct
   - Check API key permissions
   - Monitor rate limits

3. **Frontend Build Errors**
   - Ensure all dependencies are installed
   - Check TypeScript types
   - Verify import paths

### Debug Mode
Enable debug logging by setting:
```env
DEBUG=ai-chat:*
```

## 📈 Performance Optimization

### Backend
- Implement conversation cleanup for memory management
- Add database persistence for conversations
- Use Redis for session management

### Frontend
- Implement message virtualization for large conversations
- Add lazy loading for chat components
- Optimize WebSocket connection pooling

## 🔄 Updates and Maintenance

### Updating OpenAI Models
Edit `apps/backend/src/ai-chat/services/openai.service.ts`:
```typescript
model: 'gpt-4', // Change from 'gpt-3.5-turbo'
```

### Customizing AI Behavior
Modify the system prompt in the OpenAI service to change AI personality and capabilities.

### Adding Features
- Message reactions
- File uploads
- Voice messages
- Conversation sharing

## 📞 Support

For issues or questions:
1. Check the component README files
2. Review error logs
3. Test with minimal configuration
4. Check OpenAI API status
