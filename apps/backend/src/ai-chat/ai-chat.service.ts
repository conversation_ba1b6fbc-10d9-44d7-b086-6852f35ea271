import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { OpenAiService } from './services/openai.service';
import { KnowledgeBaseService } from './services/knowledge-base.service';
import { ChatMessage, Conversation } from './interfaces/chat.interface';
import { ChatMessageDto, ChatResponseDto } from './dto/chat-message.dto';

@Injectable()
export class AiChatService {
  private readonly logger = new Logger(AiChatService.name);
  private conversations: Map<string, Conversation> = new Map();

  constructor(
    private openAiService: OpenAiService,
    private knowledgeBaseService: KnowledgeBaseService
  ) {}

  async processMessage(chatMessageDto: ChatMessageDto): Promise<ChatResponseDto> {
    try {
      const { message, conversationId, userId } = chatMessageDto;
      
      // Get or create conversation
      let conversation = conversationId 
        ? this.conversations.get(conversationId)
        : null;

      if (!conversation) {
        conversation = this.createNewConversation(userId);
        this.conversations.set(conversation.id, conversation);
      }

      // Add user message to conversation
      const userMessage: ChatMessage = {
        id: uuidv4(),
        message,
        sender: 'user',
        timestamp: new Date(),
        conversationId: conversation.id,
        userId,
      };

      conversation.messages.push(userMessage);
      conversation.updatedAt = new Date();

      // Prepare conversation history for OpenAI
      const conversationHistory = conversation.messages
        .slice(-10) // Keep last 10 messages for context
        .map(msg => ({
          role: msg.sender === 'user' ? 'user' as const : 'assistant' as const,
          content: msg.message,
        }));

      // Hybrid AI Response: Check knowledge base first, then fallback to OpenAI
      const aiResponse = await this.generateHybridResponse(
        message,
        conversationHistory.slice(0, -1) // Exclude the current message
      );

      // Add AI message to conversation
      const aiMessage: ChatMessage = {
        id: uuidv4(),
        message: aiResponse.message,
        sender: 'ai',
        timestamp: new Date(),
        conversationId: conversation.id,
        userId,
      };

      conversation.messages.push(aiMessage);
      conversation.updatedAt = new Date();

      this.logger.debug(`Processed message for conversation: ${conversation.id}`);

      return {
        message: aiResponse.message,
        conversationId: conversation.id,
        timestamp: aiMessage.timestamp.toISOString(),
        sender: 'ai',
      };
    } catch (error) {
      this.logger.error('Error processing chat message:', error);
      throw error;
    }
  }

  getConversation(conversationId: string): Conversation | null {
    return this.conversations.get(conversationId) || null;
  }

  getConversationHistory(conversationId: string): ChatMessage[] {
    const conversation = this.conversations.get(conversationId);
    return conversation ? conversation.messages : [];
  }

  deleteConversation(conversationId: string): boolean {
    return this.conversations.delete(conversationId);
  }

  getUserConversations(userId: string): Conversation[] {
    return Array.from(this.conversations.values())
      .filter(conv => conv.userId === userId)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  private createNewConversation(userId?: string): Conversation {
    return {
      id: uuidv4(),
      userId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Generate hybrid response: Knowledge base first, then OpenAI fallback
   */
  private async generateHybridResponse(
    message: string,
    conversationHistory: Array<{ role: 'user' | 'assistant'; content: string }>
  ) {
    this.logger.debug(`Generating hybrid response for: "${message.substring(0, 50)}..."`);

    try {
      // Step 1: Search knowledge base
      const knowledgeResults = await this.knowledgeBaseService.search(message, {
        minScore: 0.6, // High confidence threshold
        maxResults: 1,
      });

      if (knowledgeResults.length > 0) {
        const bestMatch = knowledgeResults[0];
        this.logger.log(`Knowledge base match found (score: ${bestMatch.score.toFixed(2)}): ${bestMatch.entry.question}`);

        // For very high confidence matches, use knowledge base directly
        if (bestMatch.score >= 0.8) {
          this.logger.log('Using direct knowledge base response');
          return this.openAiService.generateKnowledgeBaseResponse(bestMatch.entry);
        }

        // For medium confidence matches, use knowledge base as context for OpenAI
        this.logger.log('Using knowledge base as context for OpenAI');
        return await this.openAiService.generateResponse(
          message,
          conversationHistory,
          bestMatch.entry
        );
      }

      // Step 2: No good knowledge base match, use pure OpenAI
      this.logger.log('No knowledge base match found, using OpenAI');
      return await this.openAiService.generateResponse(message, conversationHistory);

    } catch (error) {
      this.logger.error('Error in hybrid response generation:', error);

      // Fallback to OpenAI if knowledge base fails
      try {
        return await this.openAiService.generateResponse(message, conversationHistory);
      } catch (openAiError) {
        this.logger.error('OpenAI fallback also failed:', openAiError);

        // Final fallback response
        return {
          message: 'I apologize, but I\'m having trouble processing your request right now. Please try again later or contact our support team for assistance.',
        };
      }
    }
  }

  // Clean up old conversations (optional - for memory management)
  cleanupOldConversations(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = new Date().getTime();
    const conversationsToDelete: string[] = [];

    this.conversations.forEach((conversation, id) => {
      if (now - conversation.updatedAt.getTime() > maxAge) {
        conversationsToDelete.push(id);
      }
    });

    conversationsToDelete.forEach(id => {
      this.conversations.delete(id);
    });

    if (conversationsToDelete.length > 0) {
      this.logger.log(`Cleaned up ${conversationsToDelete.length} old conversations`);
    }
  }

  /**
   * Get knowledge base statistics for monitoring
   */
  getKnowledgeBaseStats() {
    return this.knowledgeBaseService.getStats();
  }

  /**
   * Search knowledge base directly (for testing/debugging)
   */
  async searchKnowledgeBase(query: string) {
    return this.knowledgeBaseService.search(query);
  }
}
