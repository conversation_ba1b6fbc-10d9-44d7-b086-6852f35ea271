import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { OpenAiService } from './services/openai.service';
import { ChatMessage, Conversation } from './interfaces/chat.interface';
import { ChatMessageDto, ChatResponseDto } from './dto/chat-message.dto';

@Injectable()
export class AiChatService {
  private readonly logger = new Logger(AiChatService.name);
  private conversations: Map<string, Conversation> = new Map();

  constructor(private openAiService: OpenAiService) {}

  async processMessage(chatMessageDto: ChatMessageDto): Promise<ChatResponseDto> {
    try {
      const { message, conversationId, userId } = chatMessageDto;
      
      // Get or create conversation
      let conversation = conversationId 
        ? this.conversations.get(conversationId)
        : null;

      if (!conversation) {
        conversation = this.createNewConversation(userId);
        this.conversations.set(conversation.id, conversation);
      }

      // Add user message to conversation
      const userMessage: ChatMessage = {
        id: uuidv4(),
        message,
        sender: 'user',
        timestamp: new Date(),
        conversationId: conversation.id,
        userId,
      };

      conversation.messages.push(userMessage);
      conversation.updatedAt = new Date();

      // Prepare conversation history for OpenAI
      const conversationHistory = conversation.messages
        .slice(-10) // Keep last 10 messages for context
        .map(msg => ({
          role: msg.sender === 'user' ? 'user' as const : 'assistant' as const,
          content: msg.message,
        }));

      // Generate AI response
      const aiResponse = await this.openAiService.generateResponse(
        message,
        conversationHistory.slice(0, -1) // Exclude the current message
      );

      // Add AI message to conversation
      const aiMessage: ChatMessage = {
        id: uuidv4(),
        message: aiResponse.message,
        sender: 'ai',
        timestamp: new Date(),
        conversationId: conversation.id,
        userId,
      };

      conversation.messages.push(aiMessage);
      conversation.updatedAt = new Date();

      this.logger.debug(`Processed message for conversation: ${conversation.id}`);

      return {
        message: aiResponse.message,
        conversationId: conversation.id,
        timestamp: aiMessage.timestamp.toISOString(),
        sender: 'ai',
      };
    } catch (error) {
      this.logger.error('Error processing chat message:', error);
      throw error;
    }
  }

  getConversation(conversationId: string): Conversation | null {
    return this.conversations.get(conversationId) || null;
  }

  getConversationHistory(conversationId: string): ChatMessage[] {
    const conversation = this.conversations.get(conversationId);
    return conversation ? conversation.messages : [];
  }

  deleteConversation(conversationId: string): boolean {
    return this.conversations.delete(conversationId);
  }

  getUserConversations(userId: string): Conversation[] {
    return Array.from(this.conversations.values())
      .filter(conv => conv.userId === userId)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  private createNewConversation(userId?: string): Conversation {
    return {
      id: uuidv4(),
      userId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // Clean up old conversations (optional - for memory management)
  cleanupOldConversations(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = new Date().getTime();
    const conversationsToDelete: string[] = [];

    this.conversations.forEach((conversation, id) => {
      if (now - conversation.updatedAt.getTime() > maxAge) {
        conversationsToDelete.push(id);
      }
    });

    conversationsToDelete.forEach(id => {
      this.conversations.delete(id);
    });

    if (conversationsToDelete.length > 0) {
      this.logger.log(`Cleaned up ${conversationsToDelete.length} old conversations`);
    }
  }
}
