# AI Chat Module

A complete NestJS module for AI-powered chat functionality using OpenAI and WebSockets.

## Features

- **WebSocket Support**: Real-time chat using Socket.IO
- **OpenAI Integration**: GPT-3.5-turbo powered responses
- **Rate Limiting**: Prevents spam and abuse
- **Conversation Management**: Maintains chat history
- **Error Handling**: Graceful error handling with fallback responses
- **REST API**: Alternative HTTP endpoints for chat functionality
- **Health Checks**: Monitor service status

## Architecture

```
ai-chat/
├── ai-chat.module.ts          # Main module
├── ai-chat.gateway.ts         # WebSocket gateway
├── ai-chat.service.ts         # Core chat logic
├── ai-chat.controller.ts      # REST API endpoints
├── dto/
│   └── chat-message.dto.ts    # Data transfer objects
├── interfaces/
│   └── chat.interface.ts      # TypeScript interfaces
├── services/
│   └── openai.service.ts      # OpenAI API integration
└── guards/
    └── rate-limit.guard.ts    # Rate limiting protection
```

## WebSocket Events

### Client to Server
- `sendMessage`: Send a chat message
- `getConversationHistory`: Retrieve conversation history
- `deleteConversation`: Delete a conversation
- `ping`: Health check

### Server to Client
- `connected`: Connection confirmation
- `messageResponse`: AI response to user message
- `aiTyping`: Typing indicator
- `messageError`: Error handling
- `conversationHistory`: Historical messages
- `rateLimitExceeded`: Rate limit warning
- `pong`: Health check response

## REST API Endpoints

- `GET /ai-chat/health`: Service health check
- `POST /ai-chat/message`: Send message via HTTP
- `GET /ai-chat/conversation/:id`: Get conversation details
- `GET /ai-chat/conversation/:id/history`: Get conversation history
- `DELETE /ai-chat/conversation/:id`: Delete conversation

## Configuration

The module requires the following environment variable:
- `OPENAI_API_KEY`: Your OpenAI API key

## Usage

1. Import the module in your app.module.ts
2. The WebSocket gateway will be available at `/ai-chat` namespace
3. REST endpoints will be available at `/ai-chat/*`

## Rate Limiting

- Maximum 10 requests per minute per client
- Automatic cleanup of old request records
- Graceful error messages for rate limit violations

## Error Handling

- Fallback responses when OpenAI is unavailable
- Comprehensive logging for debugging
- User-friendly error messages
- Automatic retry mechanisms
