import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { OpenAiResponse } from '../interfaces/chat.interface';

@Injectable()
export class OpenAiService {
  private readonly logger = new Logger(OpenAiService.name);
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    
    if (!apiKey) {
      this.logger.error('OpenAI API key not found in environment variables');
      throw new Error('OpenAI API key is required');
    }

    this.openai = new OpenAI({
      apiKey: apiKey,
    });

    this.logger.log('OpenAI service initialized successfully');
  }

  async generateResponse(
    message: string,
    conversationHistory: Array<{ role: 'user' | 'assistant'; content: string }> = []
  ): Promise<OpenAiResponse> {
    try {
      this.logger.debug(`Generating response for message: ${message.substring(0, 50)}...`);

      // Prepare messages for OpenAI API
      const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [
        {
          role: 'system',
          content: `You are a helpful AI assistant for a nursing platform. You can help with:
          - General nursing questions and advice
          - Healthcare information (general, not medical diagnosis)
          - Platform navigation and features
          - Professional development in nursing
          
          Please be professional, empathetic, and helpful. If asked about medical diagnosis or treatment, 
          remind users to consult with healthcare professionals.`
        },
        ...conversationHistory,
        { role: 'user', content: message }
      ];

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: 500,
        temperature: 0.7,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      });

      const responseMessage = completion.choices[0]?.message?.content;
      
      if (!responseMessage) {
        throw new Error('No response generated from OpenAI');
      }

      this.logger.debug('Successfully generated AI response');

      return {
        message: responseMessage,
        usage: completion.usage ? {
          prompt_tokens: completion.usage.prompt_tokens,
          completion_tokens: completion.usage.completion_tokens,
          total_tokens: completion.usage.total_tokens,
        } : undefined,
      };
    } catch (error) {
      this.logger.error('Error generating AI response:', error);
      
      // Return a fallback response
      return {
        message: 'I apologize, but I\'m having trouble processing your request right now. Please try again later or contact support if the issue persists.',
      };
    }
  }

  async validateApiKey(): Promise<boolean> {
    try {
      await this.openai.models.list();
      return true;
    } catch (error) {
      this.logger.error('OpenAI API key validation failed:', error);
      return false;
    }
  }
}
