import { Injectable, Logger } from '@nestjs/common';
import { KnowledgeBaseEntry, SearchResult, SearchOptions } from '../interfaces/knowledge-base.interface';

@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);

  /**
   * Search through knowledge base entries using multiple matching strategies
   */
  searchEntries(
    query: string,
    entries: KnowledgeBaseEntry[],
    options: SearchOptions = {}
  ): SearchResult[] {
    const {
      minScore = 0.3,
      maxResults = 5,
      categories = [],
      includeInactive = false,
    } = options;

    this.logger.debug(`Searching for: "${query}" with ${entries.length} entries`);

    // Filter entries based on options
    let filteredEntries = entries.filter(entry => {
      if (!includeInactive && !entry.isActive) return false;
      if (categories.length > 0 && !categories.includes(entry.category)) return false;
      return true;
    });

    const results: SearchResult[] = [];
    const queryLower = query.toLowerCase().trim();
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);

    for (const entry of filteredEntries) {
      const scores = [
        this.calculateExactMatchScore(queryLower, entry),
        this.calculateKeywordMatchScore(queryWords, entry),
        this.calculatePartialMatchScore(queryLower, entry),
        this.calculateSemanticScore(queryWords, entry),
      ];

      const maxScore = Math.max(...scores);
      const matchType = this.determineMatchType(scores);

      if (maxScore >= minScore) {
        results.push({
          entry,
          score: maxScore,
          matchType,
        });
      }
    }

    // Sort by score (descending) and priority (descending)
    results.sort((a, b) => {
      if (Math.abs(a.score - b.score) < 0.01) {
        return b.entry.priority - a.entry.priority;
      }
      return b.score - a.score;
    });

    const finalResults = results.slice(0, maxResults);
    this.logger.debug(`Found ${finalResults.length} relevant results`);

    return finalResults;
  }

  /**
   * Calculate exact match score
   */
  private calculateExactMatchScore(query: string, entry: KnowledgeBaseEntry): number {
    const questionLower = entry.question.toLowerCase();
    const answerLower = entry.answer.toLowerCase();

    if (questionLower.includes(query) || answerLower.includes(query)) {
      const questionMatch = questionLower.includes(query) ? 1.0 : 0;
      const answerMatch = answerLower.includes(query) ? 0.8 : 0;
      return Math.max(questionMatch, answerMatch);
    }

    return 0;
  }

  /**
   * Calculate keyword match score
   */
  private calculateKeywordMatchScore(queryWords: string[], entry: KnowledgeBaseEntry): number {
    if (queryWords.length === 0) return 0;

    const keywords = entry.keywords.map(k => k.toLowerCase());
    let matchCount = 0;
    let totalWeight = 0;

    for (const word of queryWords) {
      const exactMatch = keywords.includes(word);
      const partialMatch = keywords.some(keyword => 
        keyword.includes(word) || word.includes(keyword)
      );

      if (exactMatch) {
        matchCount += 1;
        totalWeight += 1;
      } else if (partialMatch) {
        matchCount += 0.5;
        totalWeight += 0.5;
      }
    }

    return queryWords.length > 0 ? (matchCount / queryWords.length) * 0.9 : 0;
  }

  /**
   * Calculate partial match score
   */
  private calculatePartialMatchScore(query: string, entry: KnowledgeBaseEntry): number {
    const questionLower = entry.question.toLowerCase();
    const answerLower = entry.answer.toLowerCase();

    let score = 0;

    // Check for partial matches in question
    const questionWords = questionLower.split(/\s+/);
    const queryWords = query.split(/\s+/);
    
    let questionMatches = 0;
    for (const qWord of queryWords) {
      if (questionWords.some(word => word.includes(qWord) || qWord.includes(word))) {
        questionMatches++;
      }
    }

    if (queryWords.length > 0) {
      score = Math.max(score, (questionMatches / queryWords.length) * 0.7);
    }

    // Check for partial matches in answer (lower weight)
    let answerMatches = 0;
    const answerWords = answerLower.split(/\s+/);
    for (const qWord of queryWords) {
      if (answerWords.some(word => word.includes(qWord) || qWord.includes(word))) {
        answerMatches++;
      }
    }

    if (queryWords.length > 0) {
      score = Math.max(score, (answerMatches / queryWords.length) * 0.5);
    }

    return score;
  }

  /**
   * Calculate semantic similarity score (simple implementation)
   */
  private calculateSemanticScore(queryWords: string[], entry: KnowledgeBaseEntry): number {
    // Simple semantic matching based on common nursing/healthcare terms
    const semanticGroups = {
      registration: ['register', 'signup', 'account', 'join', 'enroll'],
      payment: ['pay', 'money', 'cost', 'price', 'billing', 'fee'],
      support: ['help', 'contact', 'support', 'assistance', 'customer'],
      services: ['service', 'care', 'nursing', 'healthcare', 'treatment'],
      security: ['safe', 'secure', 'protection', 'privacy', 'safety'],
    };

    let semanticScore = 0;
    const entryCategory = entry.category.toLowerCase();

    for (const word of queryWords) {
      for (const [category, synonyms] of Object.entries(semanticGroups)) {
        if (synonyms.includes(word) && category === entryCategory) {
          semanticScore += 0.3;
        }
      }
    }

    return Math.min(semanticScore, 0.6); // Cap at 0.6
  }

  /**
   * Determine the type of match based on scores
   */
  private determineMatchType(scores: number[]): 'exact' | 'keyword' | 'partial' {
    const [exactScore, keywordScore, partialScore] = scores;

    if (exactScore > 0.8) return 'exact';
    if (keywordScore > partialScore) return 'keyword';
    return 'partial';
  }

  /**
   * Extract key terms from a query for better matching
   */
  extractKeyTerms(query: string): string[] {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
      'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
      'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
      'should', 'may', 'might', 'can', 'how', 'what', 'where', 'when', 'why',
      'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    ]);

    return query
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word));
  }
}
