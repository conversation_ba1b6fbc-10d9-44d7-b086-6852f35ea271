import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import { 
  KnowledgeBaseEntry, 
  SearchResult, 
  SearchOptions, 
  KnowledgeBaseStats 
} from '../interfaces/knowledge-base.interface';
import { SearchService } from './search.service';

@Injectable()
export class KnowledgeBaseService implements OnModuleInit {
  private readonly logger = new Logger(KnowledgeBaseService.name);
  private knowledgeBase: KnowledgeBaseEntry[] = [];
  private lastLoadTime: Date = new Date();

  constructor(private searchService: SearchService) {}

  async onModuleInit() {
    await this.loadKnowledgeBase();
  }

  /**
   * Load knowledge base from JSON file
   * In production, this would load from your database
   */
  private async loadKnowledgeBase(): Promise<void> {
    try {
      const filePath = join(__dirname, '..', 'data', 'mock-knowledge-base.json');
      const fileContent = readFileSync(filePath, 'utf8');
      const rawData = JSON.parse(fileContent);

      this.knowledgeBase = rawData.map((entry: any) => ({
        ...entry,
        lastUpdated: new Date(entry.lastUpdated),
      }));

      this.lastLoadTime = new Date();
      this.logger.log(`Loaded ${this.knowledgeBase.length} knowledge base entries`);
    } catch (error) {
      this.logger.error('Failed to load knowledge base:', error);
      this.knowledgeBase = [];
    }
  }

  /**
   * Search the knowledge base for relevant entries
   */
  async search(query: string, options?: SearchOptions): Promise<SearchResult[]> {
    if (!query || query.trim().length < 2) {
      return [];
    }

    this.logger.debug(`Searching knowledge base for: "${query}"`);

    const results = this.searchService.searchEntries(
      query,
      this.knowledgeBase,
      {
        minScore: 0.3,
        maxResults: 3,
        includeInactive: false,
        ...options,
      }
    );

    this.logger.debug(`Knowledge base search returned ${results.length} results`);
    return results;
  }

  /**
   * Get the best matching entry for a query
   */
  async getBestMatch(query: string): Promise<KnowledgeBaseEntry | null> {
    const results = await this.search(query, { maxResults: 1, minScore: 0.5 });
    return results.length > 0 ? results[0].entry : null;
  }

  /**
   * Check if a query has a good match in the knowledge base
   */
  async hasRelevantMatch(query: string, minScore: number = 0.5): Promise<boolean> {
    const results = await this.search(query, { maxResults: 1, minScore });
    return results.length > 0;
  }

  /**
   * Get all entries by category
   */
  getByCategory(category: string): KnowledgeBaseEntry[] {
    return this.knowledgeBase.filter(
      entry => entry.category.toLowerCase() === category.toLowerCase() && entry.isActive
    );
  }

  /**
   * Get entry by ID
   */
  getById(id: string): KnowledgeBaseEntry | null {
    return this.knowledgeBase.find(entry => entry.id === id) || null;
  }

  /**
   * Get all available categories
   */
  getCategories(): string[] {
    const categories = new Set(
      this.knowledgeBase
        .filter(entry => entry.isActive)
        .map(entry => entry.category)
    );
    return Array.from(categories).sort();
  }

  /**
   * Get knowledge base statistics
   */
  getStats(): KnowledgeBaseStats {
    const activeEntries = this.knowledgeBase.filter(entry => entry.isActive);
    
    return {
      totalEntries: this.knowledgeBase.length,
      activeEntries: activeEntries.length,
      categories: this.getCategories(),
      lastUpdated: this.lastLoadTime,
    };
  }

  /**
   * Add a new entry (for future database integration)
   */
  async addEntry(entry: Omit<KnowledgeBaseEntry, 'id' | 'lastUpdated'>): Promise<KnowledgeBaseEntry> {
    const newEntry: KnowledgeBaseEntry = {
      ...entry,
      id: `kb-${Date.now()}`,
      lastUpdated: new Date(),
    };

    this.knowledgeBase.push(newEntry);
    this.logger.log(`Added new knowledge base entry: ${newEntry.id}`);
    
    return newEntry;
  }

  /**
   * Update an existing entry (for future database integration)
   */
  async updateEntry(id: string, updates: Partial<KnowledgeBaseEntry>): Promise<KnowledgeBaseEntry | null> {
    const index = this.knowledgeBase.findIndex(entry => entry.id === id);
    
    if (index === -1) {
      return null;
    }

    this.knowledgeBase[index] = {
      ...this.knowledgeBase[index],
      ...updates,
      lastUpdated: new Date(),
    };

    this.logger.log(`Updated knowledge base entry: ${id}`);
    return this.knowledgeBase[index];
  }

  /**
   * Delete an entry (soft delete by setting isActive to false)
   */
  async deleteEntry(id: string): Promise<boolean> {
    const entry = this.knowledgeBase.find(e => e.id === id);
    
    if (!entry) {
      return false;
    }

    entry.isActive = false;
    entry.lastUpdated = new Date();
    
    this.logger.log(`Soft deleted knowledge base entry: ${id}`);
    return true;
  }

  /**
   * Reload knowledge base (useful for development)
   */
  async reload(): Promise<void> {
    this.logger.log('Reloading knowledge base...');
    await this.loadKnowledgeBase();
  }

  /**
   * Get suggested queries based on popular entries
   */
  getSuggestedQueries(limit: number = 5): string[] {
    return this.knowledgeBase
      .filter(entry => entry.isActive)
      .sort((a, b) => b.priority - a.priority)
      .slice(0, limit)
      .map(entry => entry.question);
  }
}
