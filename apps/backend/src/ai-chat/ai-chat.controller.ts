import { Controller, Get, Post, Body, Param, Delete, Query, UseGuards, Logger } from '@nestjs/common';
import { AiChatService } from './ai-chat.service';
import { OpenAiService } from './services/openai.service';
import { KnowledgeBaseService } from './services/knowledge-base.service';
import { ChatMessageDto } from './dto/chat-message.dto';

@Controller('ai-chat')
export class AiChatController {
  private readonly logger = new Logger(AiChatController.name);

  constructor(
    private readonly aiChatService: AiChatService,
    private readonly openAiService: OpenAiService,
    private readonly knowledgeBaseService: KnowledgeBaseService,
  ) {}

  @Get('health')
  async healthCheck() {
    try {
      const isOpenAiHealthy = await this.openAiService.validateApiKey();
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        services: {
          openai: isOpenAiHealthy ? 'healthy' : 'unhealthy',
          knowledgeBase: 'healthy',
        },
        knowledgeBaseStats: this.knowledgeBaseService.getStats(),
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        services: {
          openai: 'unhealthy',
          knowledgeBase: 'healthy',
        },
      };
    }
  }

  @Post('message')
  async sendMessage(@Body() chatMessageDto: ChatMessageDto) {
    try {
      return await this.aiChatService.processMessage(chatMessageDto);
    } catch (error) {
      this.logger.error('Error processing message via REST:', error);
      throw error;
    }
  }

  @Get('conversation/:id')
  getConversation(@Param('id') conversationId: string) {
    const conversation = this.aiChatService.getConversation(conversationId);
    if (!conversation) {
      return { error: 'Conversation not found' };
    }
    return conversation;
  }

  @Get('conversation/:id/history')
  getConversationHistory(@Param('id') conversationId: string) {
    return {
      conversationId,
      messages: this.aiChatService.getConversationHistory(conversationId),
    };
  }

  @Delete('conversation/:id')
  deleteConversation(@Param('id') conversationId: string) {
    const deleted = this.aiChatService.deleteConversation(conversationId);
    return {
      conversationId,
      deleted,
    };
  }

  @Get('knowledge-base/search')
  async searchKnowledgeBase(@Query('q') query: string) {
    if (!query) {
      return { error: 'Query parameter "q" is required' };
    }

    try {
      const results = await this.knowledgeBaseService.search(query);
      return {
        query,
        results: results.map(result => ({
          ...result.entry,
          score: result.score,
          matchType: result.matchType,
        })),
      };
    } catch (error) {
      this.logger.error('Error searching knowledge base:', error);
      throw error;
    }
  }

  @Get('knowledge-base/stats')
  getKnowledgeBaseStats() {
    return this.knowledgeBaseService.getStats();
  }

  @Get('knowledge-base/categories')
  getKnowledgeBaseCategories() {
    return {
      categories: this.knowledgeBaseService.getCategories(),
    };
  }

  @Get('knowledge-base/suggestions')
  getKnowledgeBaseSuggestions() {
    return {
      suggestions: this.knowledgeBaseService.getSuggestedQueries(),
    };
  }
}
