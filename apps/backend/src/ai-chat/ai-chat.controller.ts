import { Controller, Get, Post, Body, Param, Delete, UseGuards, Logger } from '@nestjs/common';
import { AiChatService } from './ai-chat.service';
import { OpenAiService } from './services/openai.service';
import { ChatMessageDto } from './dto/chat-message.dto';

@Controller('ai-chat')
export class AiChatController {
  private readonly logger = new Logger(AiChatController.name);

  constructor(
    private readonly aiChatService: AiChatService,
    private readonly openAiService: OpenAiService,
  ) {}

  @Get('health')
  async healthCheck() {
    try {
      const isOpenAiHealthy = await this.openAiService.validateApiKey();
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        services: {
          openai: isOpenAiHealthy ? 'healthy' : 'unhealthy',
        },
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        services: {
          openai: 'unhealthy',
        },
      };
    }
  }

  @Post('message')
  async sendMessage(@Body() chatMessageDto: ChatMessageDto) {
    try {
      return await this.aiChatService.processMessage(chatMessageDto);
    } catch (error) {
      this.logger.error('Error processing message via REST:', error);
      throw error;
    }
  }

  @Get('conversation/:id')
  getConversation(@Param('id') conversationId: string) {
    const conversation = this.aiChatService.getConversation(conversationId);
    if (!conversation) {
      return { error: 'Conversation not found' };
    }
    return conversation;
  }

  @Get('conversation/:id/history')
  getConversationHistory(@Param('id') conversationId: string) {
    return {
      conversationId,
      messages: this.aiChatService.getConversationHistory(conversationId),
    };
  }

  @Delete('conversation/:id')
  deleteConversation(@Param('id') conversationId: string) {
    const deleted = this.aiChatService.deleteConversation(conversationId);
    return {
      conversationId,
      deleted,
    };
  }
}
