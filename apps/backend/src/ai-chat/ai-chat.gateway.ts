import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  WebSocketServer,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Logger, UsePipes, ValidationPipe } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { AiChatService } from './ai-chat.service';
import { ChatMessageDto } from './dto/chat-message.dto';

@WebSocketGateway({
  cors: {
    origin: [
      process.env.FRONTEND_URL || 'http://localhost:3000',
      'http://localhost:3000',
      'http://localhost:4200',
      'http://127.0.0.1:3000',
    ],
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace: '/ai-chat',
})
export class AiChatGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(AiChatGateway.name);
  private connectedClients: Map<string, Socket> = new Map();

  constructor(private aiChatService: AiChatService) {}

  afterInit(server: Server) {
    this.logger.log('AI Chat WebSocket Gateway initialized');
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    this.connectedClients.set(client.id, client);
    
    // Send welcome message
    client.emit('connected', {
      message: 'Connected to AI Chat service',
      clientId: client.id,
      timestamp: new Date().toISOString(),
    });
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.connectedClients.delete(client.id);
  }

  @SubscribeMessage('sendMessage')
  @UsePipes(new ValidationPipe({ transform: true }))
  async handleMessage(
    @MessageBody() chatMessageDto: ChatMessageDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      this.logger.debug(`Received message from client ${client.id}: ${chatMessageDto.message}`);

      // Add client ID as user ID if not provided
      if (!chatMessageDto.userId) {
        chatMessageDto.userId = client.id;
      }

      // Emit typing indicator
      client.emit('aiTyping', { isTyping: true });

      // Process the message
      const response = await this.aiChatService.processMessage(chatMessageDto);

      // Stop typing indicator
      client.emit('aiTyping', { isTyping: false });

      // Send response back to client
      client.emit('messageResponse', response);

      this.logger.debug(`Sent AI response to client ${client.id}`);
    } catch (error) {
      this.logger.error(`Error handling message from client ${client.id}:`, error);
      
      // Stop typing indicator
      client.emit('aiTyping', { isTyping: false });
      
      // Send error response
      client.emit('messageError', {
        error: 'Failed to process message',
        message: 'Sorry, I encountered an error while processing your message. Please try again.',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('getConversationHistory')
  async handleGetConversationHistory(
    @MessageBody() data: { conversationId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const history = this.aiChatService.getConversationHistory(data.conversationId);
      client.emit('conversationHistory', {
        conversationId: data.conversationId,
        messages: history,
      });
    } catch (error) {
      this.logger.error(`Error getting conversation history:`, error);
      client.emit('conversationHistoryError', {
        error: 'Failed to retrieve conversation history',
      });
    }
  }

  @SubscribeMessage('deleteConversation')
  async handleDeleteConversation(
    @MessageBody() data: { conversationId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const deleted = this.aiChatService.deleteConversation(data.conversationId);
      client.emit('conversationDeleted', {
        conversationId: data.conversationId,
        success: deleted,
      });
    } catch (error) {
      this.logger.error(`Error deleting conversation:`, error);
      client.emit('conversationDeleteError', {
        error: 'Failed to delete conversation',
      });
    }
  }

  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket) {
    client.emit('pong', { timestamp: new Date().toISOString() });
  }

  // Utility method to broadcast to all connected clients
  broadcastToAll(event: string, data: any) {
    this.server.emit(event, data);
  }

  // Utility method to send message to specific client
  sendToClient(clientId: string, event: string, data: any) {
    const client = this.connectedClients.get(clientId);
    if (client) {
      client.emit(event, data);
    }
  }
}
