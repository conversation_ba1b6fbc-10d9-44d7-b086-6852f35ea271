import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AiChatGateway } from './ai-chat.gateway';
import { AiChatService } from './ai-chat.service';
import { AiChatController } from './ai-chat.controller';
import { OpenAiService } from './services/openai.service';
import { RateLimitGuard } from './guards/rate-limit.guard';

@Module({
  imports: [ConfigModule],
  controllers: [AiChatController],
  providers: [AiChatGateway, AiChatService, OpenAiService, RateLimitGuard],
  exports: [AiChatService],
})
export class AiChatModule {}
