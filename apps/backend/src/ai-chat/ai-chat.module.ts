import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AiChatGateway } from './ai-chat.gateway';
import { AiChatService } from './ai-chat.service';
import { AiChatController } from './ai-chat.controller';
import { OpenAiService } from './services/openai.service';
import { KnowledgeBaseService } from './services/knowledge-base.service';
import { SearchService } from './services/search.service';
import { RateLimitGuard } from './guards/rate-limit.guard';

@Module({
  imports: [ConfigModule],
  controllers: [AiChatController],
  providers: [
    AiChatGateway,
    AiChatService,
    OpenAiService,
    KnowledgeBaseService,
    SearchService,
    RateLimitGuard
  ],
  exports: [AiChatService, KnowledgeBaseService],
})
export class AiChatModule {}
