export interface KnowledgeBaseEntry {
  id: string;
  question: string;
  answer: string;
  keywords: string[];
  category: string;
  priority: number; // Higher number = higher priority
  lastUpdated: Date;
  isActive: boolean;
}

export interface SearchResult {
  entry: KnowledgeBaseEntry;
  score: number; // Relevance score (0-1)
  matchType: 'exact' | 'keyword' | 'partial';
}

export interface SearchOptions {
  minScore?: number;
  maxResults?: number;
  categories?: string[];
  includeInactive?: boolean;
}

export interface KnowledgeBaseStats {
  totalEntries: number;
  activeEntries: number;
  categories: string[];
  lastUpdated: Date;
}
