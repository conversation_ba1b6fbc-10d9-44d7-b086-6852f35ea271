import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Socket } from 'socket.io';

@Injectable()
export class RateLimitGuard implements CanActivate {
  private readonly logger = new Logger(RateLimitGuard.name);
  private clientRequests: Map<string, number[]> = new Map();
  private readonly maxRequests = 10; // Max requests per window
  private readonly windowMs = 60000; // 1 minute window

  canActivate(context: ExecutionContext): boolean {
    const client = context.switchToWs().getClient<Socket>();
    const clientId = client.id;
    const now = Date.now();

    // Get client's request history
    let requests = this.clientRequests.get(clientId) || [];
    
    // Remove requests outside the time window
    requests = requests.filter(timestamp => now - timestamp < this.windowMs);
    
    // Check if client has exceeded rate limit
    if (requests.length >= this.maxRequests) {
      this.logger.warn(`Rate limit exceeded for client: ${clientId}`);
      client.emit('rateLimitExceeded', {
        error: 'Rate limit exceeded',
        message: 'Too many requests. Please wait before sending another message.',
        retryAfter: this.windowMs / 1000, // seconds
      });
      return false;
    }

    // Add current request timestamp
    requests.push(now);
    this.clientRequests.set(clientId, requests);

    return true;
  }

  // Clean up old entries periodically
  cleanup() {
    const now = Date.now();
    this.clientRequests.forEach((requests, clientId) => {
      const validRequests = requests.filter(timestamp => now - timestamp < this.windowMs);
      if (validRequests.length === 0) {
        this.clientRequests.delete(clientId);
      } else {
        this.clientRequests.set(clientId, validRequests);
      }
    });
  }
}
