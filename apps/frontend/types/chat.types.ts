export interface ChatMessage {
  id: string;
  message: string;
  sender: 'user' | 'ai';
  timestamp: string;
  conversationId: string;
  userId?: string;
}

export interface ChatMessageDto {
  message: string;
  conversationId?: string;
  userId?: string;
}

export interface ChatResponseDto {
  message: string;
  conversationId: string;
  timestamp: string;
  sender: 'user' | 'ai';
}

export interface Conversation {
  id: string;
  userId?: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
}

export interface ChatError {
  error: string;
  message: string;
  timestamp?: string;
}

export interface TypingIndicator {
  isTyping: boolean;
  userId?: string;
}

export interface ConnectionStatus {
  connected: boolean;
  clientId?: string;
  timestamp?: string;
}
