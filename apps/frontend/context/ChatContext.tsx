import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ChatMessage, Conversation } from '../types/chat.types';

interface ChatContextType {
  conversations: Conversation[];
  activeConversationId: string | null;
  isFloatingChatEnabled: boolean;
  setActiveConversationId: (id: string | null) => void;
  setFloatingChatEnabled: (enabled: boolean) => void;
  addConversation: (conversation: Conversation) => void;
  updateConversation: (id: string, updates: Partial<Conversation>) => void;
  deleteConversation: (id: string) => void;
  addMessageToConversation: (conversationId: string, message: ChatMessage) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [isFloatingChatEnabled, setFloatingChatEnabled] = useState(true);

  // Load conversations from localStorage on mount
  useEffect(() => {
    const savedConversations = localStorage.getItem('ai-chat-conversations');
    if (savedConversations) {
      try {
        setConversations(JSON.parse(savedConversations));
      } catch (error) {
        console.error('Failed to load conversations from localStorage:', error);
      }
    }

    const savedFloatingChatEnabled = localStorage.getItem('ai-chat-floating-enabled');
    if (savedFloatingChatEnabled !== null) {
      setFloatingChatEnabled(JSON.parse(savedFloatingChatEnabled));
    }
  }, []);

  // Save conversations to localStorage when they change
  useEffect(() => {
    localStorage.setItem('ai-chat-conversations', JSON.stringify(conversations));
  }, [conversations]);

  // Save floating chat preference
  useEffect(() => {
    localStorage.setItem('ai-chat-floating-enabled', JSON.stringify(isFloatingChatEnabled));
  }, [isFloatingChatEnabled]);

  const addConversation = (conversation: Conversation) => {
    setConversations(prev => [conversation, ...prev]);
  };

  const updateConversation = (id: string, updates: Partial<Conversation>) => {
    setConversations(prev =>
      prev.map(conv =>
        conv.id === id
          ? { ...conv, ...updates, updatedAt: new Date().toISOString() }
          : conv
      )
    );
  };

  const deleteConversation = (id: string) => {
    setConversations(prev => prev.filter(conv => conv.id !== id));
    if (activeConversationId === id) {
      setActiveConversationId(null);
    }
  };

  const addMessageToConversation = (conversationId: string, message: ChatMessage) => {
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? {
              ...conv,
              messages: [...conv.messages, message],
              updatedAt: new Date().toISOString(),
            }
          : conv
      )
    );
  };

  const value: ChatContextType = {
    conversations,
    activeConversationId,
    isFloatingChatEnabled,
    setActiveConversationId,
    setFloatingChatEnabled,
    addConversation,
    updateConversation,
    deleteConversation,
    addMessageToConversation,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
