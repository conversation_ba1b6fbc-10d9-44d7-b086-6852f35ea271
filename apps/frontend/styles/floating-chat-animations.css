/* Floating Chat Widget Animations */

/* Chat popup slide-in animation */
@keyframes slideInUp {
  from {
    transform: translateY(100%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateY(100%) scale(0.8);
    opacity: 0;
  }
}

/* Chat icon bounce animation */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Pulse animation for notifications */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Ripple effect for button clicks */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Fade in animation for tooltips */
@keyframes fadeInUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Typing indicator animation */
@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Gradient animation for chat icon */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Floating chat widget classes */
.floating-chat-popup {
  animation: slideInUp 0.3s ease-out;
}

.floating-chat-popup.closing {
  animation: slideOutDown 0.3s ease-in;
}

.floating-chat-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-chat-icon:hover {
  animation: bounce 1s ease-in-out;
}

.floating-chat-icon.has-notification {
  animation: pulse 2s infinite;
}

.floating-chat-tooltip {
  animation: fadeInUp 0.3s ease-out;
}

.floating-chat-ripple {
  animation: ripple 0.6s linear;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .floating-chat-popup {
    width: calc(100vw - 2rem) !important;
    height: calc(100vh - 8rem) !important;
    bottom: 1rem !important;
    right: 1rem !important;
    left: 1rem !important;
    max-width: none !important;
  }
  
  .floating-chat-icon {
    width: 3.5rem !important;
    height: 3.5rem !important;
  }
  
  .floating-chat-tooltip {
    max-width: calc(100vw - 6rem) !important;
    font-size: 0.75rem !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .floating-chat-tooltip {
    background-color: #1f2937;
    color: #f9fafb;
    border-color: #374151;
  }
  
  .floating-chat-tooltip .tooltip-arrow {
    border-top-color: #1f2937;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .floating-chat-icon {
    border: 2px solid currentColor;
  }
  
  .floating-chat-popup {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .floating-chat-popup,
  .floating-chat-icon,
  .floating-chat-tooltip {
    animation: none !important;
    transition: none !important;
  }
  
  .floating-chat-icon:hover {
    transform: scale(1.05);
  }
}

/* Focus styles for accessibility */
.floating-chat-icon:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.floating-chat-icon:focus:not(:focus-visible) {
  outline: none;
}

/* Custom scrollbar for chat messages */
.floating-chat-messages::-webkit-scrollbar {
  width: 6px;
}

.floating-chat-messages::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.floating-chat-messages::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.floating-chat-messages::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading spinner for connection states */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.floating-chat-spinner {
  animation: spin 1s linear infinite;
}

/* Message bubble animations */
@keyframes messageSlideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.floating-chat-message {
  animation: messageSlideIn 0.3s ease-out;
}

/* Status indicator animations */
@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.floating-chat-status-online {
  animation: statusPulse 2s ease-in-out infinite;
}
