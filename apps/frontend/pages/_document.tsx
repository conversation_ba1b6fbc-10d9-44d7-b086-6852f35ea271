import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="ar" dir="rtl">
      <Head>
        {/* Meta tags for better SEO and performance */}
        <meta charSet="utf-8" />
        <meta name="theme-color" content="#3B82F6" />
        
        {/* Preconnect to external domains for better performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/favicon.ico" />
        
        {/* App manifest for PWA support */}
        <link rel="manifest" href="/manifest.json" />
        
        {/* Fonts - Arabic and English support */}
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
        
        {/* Additional meta tags for the nursing platform */}
        <meta name="description" content="منصة ربط المرضى بالممرضين المؤهلين - NurseConnect" />
        <meta name="keywords" content="ممرضين, رعاية صحية, خدمات طبية, تمريض منزلي" />
        <meta name="author" content="NurseConnect Team" />
        
        {/* Open Graph meta tags for social sharing */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content="NurseConnect - منصة ربط المرضى بالممرضين" />
        <meta property="og:description" content="منصة موثوقة لربط المرضى بالممرضين المؤهلين" />
        <meta property="og:image" content="/imagenurse.jpg" />
        
        {/* Twitter Card meta tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="NurseConnect" />
        <meta name="twitter:description" content="منصة ربط المرضى بالممرضين المؤهلين" />
        <meta name="twitter:image" content="/imagenurse.jpg" />
      </Head>
      <body className="bg-gray-50 text-gray-900">
        {/* Loading indicator */}
        <div id="loading-indicator" className="fixed inset-0 bg-white z-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        
        <Main />
        <NextScript />
        
        {/* Remove loading indicator after page loads */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.addEventListener('load', function() {
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                  loadingIndicator.style.display = 'none';
                }
              });
            `,
          }}
        />
      </body>
    </Html>
  );
}
