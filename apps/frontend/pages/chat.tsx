import React, { useState } from 'react';
import Head from 'next/head';
import { ChatInterface } from '../components/ai-chat/ChatInterface';
import Layout from '../components/Layout';

const ChatPage: React.FC = () => {
  const [isChatOpen, setIsChatOpen] = useState(true);

  return (
    <>
      <Head>
        <title>AI Chat Assistant - Nurse Platform</title>
        <meta name="description" content="Chat with our AI assistant for nursing and healthcare support" />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50">
          <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
              {/* Header */}
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  AI Chat Assistant
                </h1>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Get instant help with nursing questions, healthcare information, 
                  and platform guidance from our AI-powered assistant.
                </p>
              </div>

              {/* Chat Interface */}
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                {isChatOpen ? (
                  <div style={{ height: '600px' }}>
                    <ChatInterface
                      className="h-full"
                      onClose={() => setIsChatOpen(false)}
                    />
                  </div>
                ) : (
                  <div className="p-8 text-center">
                    <div className="text-6xl mb-4">🤖</div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                      AI Assistant
                    </h2>
                    <p className="text-gray-600 mb-6">
                      Ready to help you with nursing and healthcare questions
                    </p>
                    <button
                      onClick={() => setIsChatOpen(true)}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
                    >
                      Start Chatting
                    </button>
                  </div>
                )}
              </div>

              {/* Features */}
              <div className="mt-12 grid md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-white rounded-lg shadow">
                  <div className="text-3xl mb-3">💬</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Real-time Chat
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Instant responses powered by advanced AI technology
                  </p>
                </div>

                <div className="text-center p-6 bg-white rounded-lg shadow">
                  <div className="text-3xl mb-3">🏥</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Healthcare Expertise
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Specialized knowledge in nursing and healthcare topics
                  </p>
                </div>

                <div className="text-center p-6 bg-white rounded-lg shadow">
                  <div className="text-3xl mb-3">🔒</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Secure & Private
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Your conversations are secure and confidential
                  </p>
                </div>
              </div>

              {/* Disclaimer */}
              <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start">
                  <div className="text-yellow-600 mr-3 mt-1">⚠️</div>
                  <div>
                    <h4 className="text-sm font-semibold text-yellow-800 mb-1">
                      Important Disclaimer
                    </h4>
                    <p className="text-sm text-yellow-700">
                      This AI assistant provides general information and guidance only. 
                      For medical diagnosis, treatment decisions, or emergency situations, 
                      always consult with qualified healthcare professionals.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default ChatPage;
