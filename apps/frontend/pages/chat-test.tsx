import React, { useState } from 'react';
import Head from 'next/head';
import Layout from '../components/Layout';
import { FloatingChatWidget } from '../components/ai-chat/FloatingChatWidget';
import { ChatDebug } from '../components/ai-chat/ChatDebug';

const ChatTestPage: React.FC = () => {
  const [showDebug, setShowDebug] = useState(true);
  const [widgetEnabled, setWidgetEnabled] = useState(true);

  return (
    <>
      <Head>
        <title>Chat Test - Nurse Platform</title>
        <meta name="description" content="Test page for AI chat functionality" />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50 p-8">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                AI Chat Test Page
              </h1>
              <p className="text-lg text-gray-600">
                This page is for testing the AI chat widget functionality
              </p>
            </div>

            {/* Controls */}
            <div className="bg-white rounded-lg shadow p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Controls</h2>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showDebug}
                      onChange={(e) => setShowDebug(e.target.checked)}
                      className="mr-2"
                    />
                    Show Debug Info
                  </label>
                </div>

                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={widgetEnabled}
                      onChange={(e) => setWidgetEnabled(e.target.checked)}
                      className="mr-2"
                    />
                    Enable Chat Widget
                  </label>
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold text-blue-900 mb-4">How to Test</h2>
              <ol className="list-decimal list-inside space-y-2 text-blue-800">
                <li>Look for the floating chat icon in the bottom-right corner</li>
                <li>Click the chat icon to open the chat popup</li>
                <li>Try sending a message like "How do I register as a nurse?"</li>
                <li>Check if you get a response from the AI</li>
                <li>Test mobile responsiveness by resizing your browser window</li>
              </ol>
            </div>

            {/* Expected Behavior */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold text-green-900 mb-4">Expected Behavior</h2>
              <ul className="list-disc list-inside space-y-2 text-green-800">
                <li>Chat icon should appear as a blue circular button</li>
                <li>Clicking opens a chat popup window</li>
                <li>Messages about registration should get knowledge base responses</li>
                <li>Other messages should get OpenAI responses</li>
                <li>On mobile, chat should open full-screen</li>
              </ul>
            </div>

            {/* Troubleshooting */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-yellow-900 mb-4">Troubleshooting</h2>
              <ul className="list-disc list-inside space-y-2 text-yellow-800">
                <li>If no chat icon appears, check the debug info above</li>
                <li>If chat doesn't connect, ensure backend is running on port 3001</li>
                <li>If no responses, check browser console for errors</li>
                <li>If styling looks broken, ensure CSS files are imported</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Debug Info */}
        {showDebug && <ChatDebug />}

        {/* Standalone Chat Widget for Testing */}
        {widgetEnabled && (
          <FloatingChatWidget 
            enabled={true}
            position="bottom-right"
            autoOpenDelay={5000} // Open after 5 seconds for testing
            showWelcomeTooltip={true}
          />
        )}
      </Layout>
    </>
  );
};

export default ChatTestPage;
