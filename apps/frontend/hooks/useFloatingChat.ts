import { useState, useEffect, useCallback } from 'react';

interface UseFloatingChatProps {
  defaultOpen?: boolean;
  autoOpenDelay?: number; // Auto-open after X milliseconds
  persistState?: boolean; // Remember open/closed state
}

interface UseFloatingChatReturn {
  isOpen: boolean;
  isMinimized: boolean;
  hasNotification: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
  minimize: () => void;
  restore: () => void;
  clearNotification: () => void;
  setNotification: (hasNotification: boolean) => void;
}

export const useFloatingChat = ({
  defaultOpen = false,
  autoOpenDelay,
  persistState = true,
}: UseFloatingChatProps = {}): UseFloatingChatReturn => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [isMinimized, setIsMinimized] = useState(false);
  const [hasNotification, setHasNotification] = useState(false);

  // Load persisted state on mount
  useEffect(() => {
    if (persistState && typeof window !== 'undefined') {
      const savedState = localStorage.getItem('floating-chat-state');
      if (savedState) {
        try {
          const { isOpen: savedIsOpen, isMinimized: savedIsMinimized } = JSON.parse(savedState);
          setIsOpen(savedIsOpen);
          setIsMinimized(savedIsMinimized);
        } catch (error) {
          console.warn('Failed to load floating chat state:', error);
        }
      }
    }
  }, [persistState]);

  // Save state when it changes
  useEffect(() => {
    if (persistState && typeof window !== 'undefined') {
      const state = { isOpen, isMinimized };
      localStorage.setItem('floating-chat-state', JSON.stringify(state));
    }
  }, [isOpen, isMinimized, persistState]);

  // Auto-open after delay
  useEffect(() => {
    if (autoOpenDelay && !isOpen && typeof window !== 'undefined') {
      const timer = setTimeout(() => {
        setIsOpen(true);
        setHasNotification(true);
      }, autoOpenDelay);

      return () => clearTimeout(timer);
    }
  }, [autoOpenDelay, isOpen]);

  // Clear notification when chat is opened
  useEffect(() => {
    if (isOpen && !isMinimized) {
      setHasNotification(false);
    }
  }, [isOpen, isMinimized]);

  const open = useCallback(() => {
    setIsOpen(true);
    setIsMinimized(false);
    setHasNotification(false);
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
    setIsMinimized(false);
  }, []);

  const toggle = useCallback(() => {
    if (isOpen) {
      close();
    } else {
      open();
    }
  }, [isOpen, open, close]);

  const minimize = useCallback(() => {
    setIsMinimized(true);
    setIsOpen(false);
  }, []);

  const restore = useCallback(() => {
    setIsMinimized(false);
    setIsOpen(true);
    setHasNotification(false);
  }, []);

  const clearNotification = useCallback(() => {
    setHasNotification(false);
  }, []);

  const setNotification = useCallback((notification: boolean) => {
    setHasNotification(notification);
  }, []);

  return {
    isOpen,
    isMinimized,
    hasNotification,
    open,
    close,
    toggle,
    minimize,
    restore,
    clearNotification,
    setNotification,
  };
};
