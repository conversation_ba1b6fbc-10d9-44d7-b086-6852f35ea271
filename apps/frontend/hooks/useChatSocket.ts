import { useEffect, useState, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { ChatMessage, ChatMessageDto, ChatResponseDto } from '../types/chat.types';

interface UseChatSocketProps {
  onMessageReceived?: (message: ChatMessage) => void;
  onTypingIndicator?: (isTyping: boolean) => void;
  onError?: (error: string) => void;
  onConnected?: () => void;
  onDisconnected?: () => void;
}

interface UseChatSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  sendMessage: (message: ChatMessageDto) => Promise<ChatResponseDto | null>;
  error: string | null;
  clearError: () => void;
  disconnect: () => void;
}

export const useChatSocket = ({
  onMessageReceived,
  onTypingIndicator,
  onError,
  onConnected,
  onDisconnected,
}: UseChatSocketProps): UseChatSocketReturn => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagePromiseRef = useRef<{
    resolve: (value: ChatResponseDto | null) => void;
    reject: (reason?: any) => void;
  } | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  useEffect(() => {
    // Create socket connection
    const newSocket = io(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/ai-chat`, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('Connected to AI chat service');
      setIsConnected(true);
      setError(null);
      onConnected?.();
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from AI chat service');
      setIsConnected(false);
      onDisconnected?.();
    });

    newSocket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      setError('Failed to connect to chat service');
      setIsConnected(false);
      onError?.('Failed to connect to chat service');
    });

    // Chat event handlers
    newSocket.on('connected', (data) => {
      console.log('Chat service ready:', data);
    });

    newSocket.on('messageResponse', (response: ChatResponseDto) => {
      console.log('Received AI response:', response);
      
      // Convert response to ChatMessage format
      const chatMessage: ChatMessage = {
        id: Date.now().toString(),
        message: response.message,
        sender: 'ai',
        timestamp: response.timestamp,
        conversationId: response.conversationId,
      };

      onMessageReceived?.(chatMessage);

      // Resolve promise if waiting for response
      if (messagePromiseRef.current) {
        messagePromiseRef.current.resolve(response);
        messagePromiseRef.current = null;
      }
    });

    newSocket.on('aiTyping', (data: { isTyping: boolean }) => {
      onTypingIndicator?.(data.isTyping);
    });

    newSocket.on('messageError', (errorData: { error: string; message: string }) => {
      console.error('Message error:', errorData);
      setError(errorData.message || errorData.error);
      onError?.(errorData.message || errorData.error);

      // Reject promise if waiting for response
      if (messagePromiseRef.current) {
        messagePromiseRef.current.reject(new Error(errorData.error));
        messagePromiseRef.current = null;
      }
    });

    newSocket.on('rateLimitExceeded', (data: { error: string; message: string }) => {
      console.warn('Rate limit exceeded:', data);
      setError(data.message);
      onError?.(data.message);
    });

    setSocket(newSocket);

    // Cleanup on unmount
    return () => {
      newSocket.disconnect();
    };
  }, [onMessageReceived, onTypingIndicator, onError, onConnected, onDisconnected]);

  const sendMessage = useCallback(
    async (messageDto: ChatMessageDto): Promise<ChatResponseDto | null> => {
      if (!socket || !isConnected) {
        throw new Error('Not connected to chat service');
      }

      return new Promise((resolve, reject) => {
        // Store promise resolvers
        messagePromiseRef.current = { resolve, reject };

        // Set timeout for response
        const timeout = setTimeout(() => {
          if (messagePromiseRef.current) {
            messagePromiseRef.current.reject(new Error('Message timeout'));
            messagePromiseRef.current = null;
          }
        }, 30000); // 30 second timeout

        // Clear timeout when promise resolves/rejects
        const originalResolve = resolve;
        const originalReject = reject;

        messagePromiseRef.current.resolve = (value) => {
          clearTimeout(timeout);
          originalResolve(value);
        };

        messagePromiseRef.current.reject = (reason) => {
          clearTimeout(timeout);
          originalReject(reason);
        };

        // Send message
        socket.emit('sendMessage', messageDto);
      });
    },
    [socket, isConnected]
  );

  const disconnect = useCallback(() => {
    if (socket) {
      socket.disconnect();
    }
  }, [socket]);

  return {
    socket,
    isConnected,
    sendMessage,
    error,
    clearError,
    disconnect,
  };
};
