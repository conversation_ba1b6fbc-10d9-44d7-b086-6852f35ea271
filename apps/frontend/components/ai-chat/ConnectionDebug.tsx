import React, { useState, useEffect } from 'react';
import { useChatSocket } from '../../hooks/useChatSocket';

interface ConnectionDebugProps {
  onClose?: () => void;
}

export const ConnectionDebug: React.FC<ConnectionDebugProps> = ({ onClose }) => {
  const [logs, setLogs] = useState<string[]>([]);
  const [testMessage, setTestMessage] = useState('Hello, this is a test message');

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-20), `[${timestamp}] ${message}`]);
  };

  const { socket, isConnected, sendMessage, error, clearError } = useChatSocket({
    onConnected: () => addLog('✅ Connected to chat service'),
    onDisconnected: () => addLog('❌ Disconnected from chat service'),
    onError: (error) => addLog(`❌ Error: ${error}`),
    onMessageReceived: (message) => addLog(`📥 Received: ${message.message}`),
    onTypingIndicator: (isTyping) => addLog(`⌨️ AI typing: ${isTyping}`),
  });

  useEffect(() => {
    addLog('🔄 Initializing connection debug...');
  }, []);

  const handleSendTest = async () => {
    if (!isConnected) {
      addLog('❌ Cannot send message - not connected');
      return;
    }

    try {
      addLog(`📤 Sending: ${testMessage}`);
      await sendMessage({
        message: testMessage,
        userId: 'debug-user',
        conversationId: 'debug-conversation',
      });
    } catch (error) {
      addLog(`❌ Send error: ${error}`);
    }
  };

  const handleClearLogs = () => {
    setLogs([]);
    addLog('🧹 Logs cleared');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">WebSocket Connection Debug</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl"
            >
              ×
            </button>
          )}
        </div>

        {/* Connection Status */}
        <div className="mb-4 p-3 rounded-lg bg-gray-50">
          <div className="flex items-center gap-2 mb-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="font-medium">
              Status: {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          {socket && (
            <div className="text-sm text-gray-600">
              Socket ID: {socket.id || 'Not available'}
            </div>
          )}
          {error && (
            <div className="text-sm text-red-600 mt-1">
              Error: {error}
              <button
                onClick={clearError}
                className="ml-2 text-blue-600 hover:underline"
              >
                Clear
              </button>
            </div>
          )}
        </div>

        {/* Test Message */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Test Message:</label>
          <div className="flex gap-2">
            <input
              type="text"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Enter test message..."
            />
            <button
              onClick={handleSendTest}
              disabled={!isConnected}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300"
            >
              Send
            </button>
          </div>
        </div>

        {/* Logs */}
        <div className="flex-1 overflow-hidden flex flex-col">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-medium">Connection Logs:</h3>
            <button
              onClick={handleClearLogs}
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Clear Logs
            </button>
          </div>
          <div className="flex-1 bg-gray-900 text-green-400 p-3 rounded-md overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <div className="text-gray-500">No logs yet...</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-4 flex gap-2">
          <button
            onClick={() => window.location.reload()}
            className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
          >
            Reload Page
          </button>
          <button
            onClick={() => {
              addLog('🔄 Attempting manual reconnection...');
              socket?.disconnect();
              setTimeout(() => socket?.connect(), 1000);
            }}
            className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
          >
            Reconnect
          </button>
        </div>
      </div>
    </div>
  );
};
