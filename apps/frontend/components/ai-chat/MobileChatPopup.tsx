import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>ontainer,
  ChatContainer,
  MessageList,
  Message,
  MessageInput,
  TypingIndicator,
  Avatar,
} from '@chatscope/chat-ui-kit-react';
import { useChatSocket } from '../../hooks/useChatSocket';
import { useTouchGestures } from '../../hooks/useTouchGestures';
import { ChatMessage } from '../../types/chat.types';

interface MobileChatPopupProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const MobileChatPopup: React.FC<MobileChatPopupProps> = ({
  isOpen,
  onClose,
  className = '',
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);

  const {
    socket,
    isConnected,
    sendMessage,
    error,
    clearError,
  } = useChatSocket({
    onMessageReceived: (message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
      setIsTyping(false);
    },
    onTypingIndicator: (typing: boolean) => {
      setIsTyping(typing);
    },
    onError: (error: string) => {
      console.error('Chat error:', error);
      setIsTyping(false);
    },
  });

  // Touch gestures for mobile interaction
  const { attachGestures } = useTouchGestures({
    onSwipeDown: () => {
      // Close chat on swipe down from header
      onClose();
    },
  });

  // Detect virtual keyboard on mobile
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        const viewportHeight = window.visualViewport?.height || window.innerHeight;
        const windowHeight = window.innerHeight;
        const keyboardHeight = windowHeight - viewportHeight;
        
        setIsKeyboardOpen(keyboardHeight > 150); // Threshold for keyboard detection
      }
    };

    if (typeof window !== 'undefined' && window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleResize);
      return () => window.visualViewport?.removeEventListener('resize', handleResize);
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (popupRef.current) {
      attachGestures(popupRef.current);
    }
  }, [attachGestures]);

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || !isConnected) return;

    // Add user message to UI immediately
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: message.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      conversationId: conversationId || '',
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Send message via socket
    try {
      const response = await sendMessage({
        message: message.trim(),
        conversationId: conversationId || undefined,
      });

      if (response?.conversationId && !conversationId) {
        setConversationId(response.conversationId);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      setIsTyping(false);
    }
  };

  const handleClearChat = () => {
    setMessages([]);
    setConversationId(null);
    clearError();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
        onClick={onClose}
      />
      
      {/* Mobile Chat Popup */}
      <div 
        ref={popupRef}
        className={`
          fixed inset-x-0 bottom-0 z-50
          bg-white rounded-t-3xl shadow-2xl
          transform transition-all duration-300 ease-out
          ${isOpen ? 'translate-y-0' : 'translate-y-full'}
          ${isKeyboardOpen ? 'h-screen' : 'h-[85vh]'}
          ${className}
        `}
        style={{
          maxHeight: isKeyboardOpen ? '100vh' : '85vh',
        }}
      >
        {/* Mobile Header with Drag Handle */}
        <div className="flex flex-col bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-3xl">
          {/* Drag Handle */}
          <div className="flex justify-center pt-3 pb-2">
            <div className="w-12 h-1 bg-white bg-opacity-30 rounded-full"></div>
          </div>
          
          {/* Header Content */}
          <div className="flex items-center justify-between px-6 pb-4">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Avatar
                  src="/ai-avatar.png"
                  name="AI Assistant"
                  size="sm"
                  className="border-2 border-white"
                />
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border border-white"></div>
              </div>
              <div>
                <h3 className="font-semibold">AI Assistant</h3>
                <p className="text-xs opacity-90">
                  {isConnected ? 'Online' : 'Connecting...'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={handleClearChat}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                title="Clear chat"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
              
              <button
                onClick={onClose}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                title="Close"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Chat Content */}
        <div className="flex-1 flex flex-col h-full">
          <MainContainer>
            <ChatContainer>
              <MessageList
                typingIndicator={
                  isTyping ? (
                    <TypingIndicator content="AI Assistant is typing..." />
                  ) : null
                }
                className="px-4 py-2"
                style={{ 
                  height: isKeyboardOpen ? 'calc(100vh - 200px)' : 'calc(85vh - 200px)',
                  overflowY: 'auto',
                }}
              >
                {messages.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
                    <div className="text-5xl mb-4">🤖</div>
                    <h4 className="font-semibold text-gray-700 mb-3 text-lg">Hello! I'm your AI assistant</h4>
                    <p className="text-center text-gray-600 mb-6 leading-relaxed">
                      I can help you with nursing questions, platform guidance, and healthcare information.
                    </p>
                    <div className="bg-gray-50 rounded-lg p-4 w-full max-w-sm">
                      <p className="text-sm font-medium text-gray-700 mb-2">Try asking:</p>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p>• "How do I register as a nurse?"</p>
                        <p>• "What services are available?"</p>
                        <p>• "How does payment work?"</p>
                      </div>
                    </div>
                  </div>
                )}

                {messages.map((msg) => (
                  <Message
                    key={msg.id}
                    model={{
                      message: msg.message,
                      sentTime: new Date(msg.timestamp).toLocaleTimeString(),
                      sender: msg.sender === 'user' ? 'user' : 'ai',
                      direction: msg.sender === 'user' ? 'outgoing' : 'incoming',
                      position: 'single',
                    }}
                  >
                    {msg.sender === 'ai' && (
                      <Avatar
                        src="/ai-avatar.png"
                        name="AI"
                        size="sm"
                      />
                    )}
                  </Message>
                ))}
                <div ref={messagesEndRef} />
              </MessageList>

              <div className="border-t bg-white">
                <MessageInput
                  placeholder="Type your message..."
                  onSend={handleSendMessage}
                  disabled={!isConnected}
                  attachButton={false}
                  style={{
                    fontSize: '16px', // Prevents zoom on iOS
                  }}
                />
              </div>
            </ChatContainer>
          </MainContainer>
        </div>

        {/* Error Message */}
        {error && (
          <div className="absolute top-20 left-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-sm z-10">
            <div className="flex justify-between items-center">
              <span>{error}</span>
              <button
                onClick={clearError}
                className="text-red-700 hover:text-red-900 ml-2 text-lg"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Connection Status */}
        {!isConnected && (
          <div className="absolute top-20 left-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg text-sm z-10">
            <span>Connecting to chat service...</span>
          </div>
        )}
      </div>
    </>
  );
};
