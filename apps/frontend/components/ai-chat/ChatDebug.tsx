import React, { useState, useEffect } from 'react';

export const ChatDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    // Check if all required dependencies are available
    const checkDependencies = () => {
      const info: any = {
        timestamp: new Date().toISOString(),
        environment: {
          nodeEnv: process.env.NODE_ENV,
          apiUrl: process.env.NEXT_PUBLIC_API_URL,
          frontendUrl: process.env.NEXT_PUBLIC_FRONTEND_URL,
        },
        browser: {
          userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'SSR',
          windowSize: typeof window !== 'undefined' ? `${window.innerWidth}x${window.innerHeight}` : 'SSR',
        },
        dependencies: {
          react: typeof React !== 'undefined',
          socketIo: false,
          chatscope: false,
        },
        errors: [],
      };

      // Check for socket.io-client
      try {
        require('socket.io-client');
        info.dependencies.socketIo = true;
      } catch (e) {
        info.errors.push('socket.io-client not found');
      }

      // Check for @chatscope/chat-ui-kit-react
      try {
        require('@chatscope/chat-ui-kit-react');
        info.dependencies.chatscope = true;
      } catch (e) {
        info.errors.push('@chatscope/chat-ui-kit-react not found');
      }

      setDebugInfo(info);
    };

    checkDependencies();
  }, []);

  return (
    <div className="fixed top-4 left-4 z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">Chat Debug Info</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Environment:</strong>
          <ul className="ml-4 mt-1">
            <li>API URL: {debugInfo.environment?.apiUrl || 'Not set'}</li>
            <li>Node ENV: {debugInfo.environment?.nodeEnv || 'Not set'}</li>
          </ul>
        </div>

        <div>
          <strong>Dependencies:</strong>
          <ul className="ml-4 mt-1">
            <li className={debugInfo.dependencies?.socketIo ? 'text-green-600' : 'text-red-600'}>
              Socket.IO: {debugInfo.dependencies?.socketIo ? '✅' : '❌'}
            </li>
            <li className={debugInfo.dependencies?.chatscope ? 'text-green-600' : 'text-red-600'}>
              Chatscope: {debugInfo.dependencies?.chatscope ? '✅' : '❌'}
            </li>
          </ul>
        </div>

        <div>
          <strong>Browser:</strong>
          <ul className="ml-4 mt-1">
            <li>Size: {debugInfo.browser?.windowSize}</li>
          </ul>
        </div>

        {debugInfo.errors?.length > 0 && (
          <div>
            <strong className="text-red-600">Errors:</strong>
            <ul className="ml-4 mt-1 text-red-600">
              {debugInfo.errors.map((error: string, index: number) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}
      </div>

      <div className="mt-3 pt-3 border-t border-gray-200">
        <button
          onClick={() => {
            console.log('Chat Debug Info:', debugInfo);
            alert('Debug info logged to console');
          }}
          className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
        >
          Log to Console
        </button>
      </div>
    </div>
  );
};
