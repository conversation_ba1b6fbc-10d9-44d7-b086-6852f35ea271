# AI Chat Frontend Components

A complete Next.js chat interface using @chatscope/chat-ui-kit-react and socket.io-client.

## Components

### ChatInterface
The main chat component with full conversation functionality.

**Features:**
- Real-time messaging with WebSocket connection
- Typing indicators
- Message history
- Error handling and connection status
- Clear chat functionality
- Responsive design

**Usage:**
```tsx
import { ChatInterface } from '../components/ai-chat/ChatInterface';

<ChatInterface 
  className="h-full"
  onClose={() => setIsChatOpen(false)}
/>
```

### FloatingChatWidget
A floating chat widget that can be placed anywhere on the page.

**Features:**
- Toggleable chat window
- Notification badges
- Customizable positioning
- Hover tooltips
- Smooth animations

**Usage:**
```tsx
import { FloatingChatWidget } from '../components/ai-chat/FloatingChatWidget';

<FloatingChatWidget 
  enabled={true}
  position="bottom-right"
/>
```

## Hooks

### useChatSocket
Custom hook for managing WebSocket connections and chat functionality.

**Features:**
- Automatic connection management
- Message sending with promises
- Error handling
- Typing indicators
- Connection status tracking

**Usage:**
```tsx
const {
  socket,
  isConnected,
  sendMessage,
  error,
  clearError,
} = useChatSocket({
  onMessageReceived: (message) => console.log(message),
  onTypingIndicator: (typing) => setIsTyping(typing),
  onError: (error) => console.error(error),
});
```

## Context

### ChatContext
React context for managing chat state across the application.

**Features:**
- Conversation management
- Local storage persistence
- Floating chat preferences
- Message history

**Usage:**
```tsx
import { ChatProvider, useChat } from '../context/ChatContext';

// Wrap your app
<ChatProvider>
  <App />
</ChatProvider>

// Use in components
const { conversations, activeConversationId } = useChat();
```

## Types

### ChatMessage
```typescript
interface ChatMessage {
  id: string;
  message: string;
  sender: 'user' | 'ai';
  timestamp: string;
  conversationId: string;
  userId?: string;
}
```

### ChatMessageDto
```typescript
interface ChatMessageDto {
  message: string;
  conversationId?: string;
  userId?: string;
}
```

## Environment Variables

Add to your `.env.local`:
```
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## Styling

The components use:
- **@chatscope/chat-ui-kit-styles**: Pre-built chat UI styles
- **Tailwind CSS**: Custom styling and layout
- **CSS Modules**: Component-specific styles

## Features

### Real-time Communication
- WebSocket connection to backend
- Automatic reconnection
- Message delivery confirmation
- Typing indicators

### User Experience
- Smooth animations
- Loading states
- Error handling
- Responsive design
- Accessibility support

### Data Management
- Local storage for conversations
- Message history
- Conversation persistence
- State management

## Integration

### Full Page Chat
Use the `/chat` page for a dedicated chat experience.

### Floating Widget
Add the floating widget to any page for quick access.

### Custom Integration
Use the `ChatInterface` component in modals, sidebars, or custom layouts.

## Error Handling

The components handle various error scenarios:
- Connection failures
- Message sending errors
- Rate limiting
- Network timeouts
- Invalid responses

## Performance

- Lazy loading of chat components
- Message virtualization for large conversations
- Efficient WebSocket connection management
- Local storage optimization
