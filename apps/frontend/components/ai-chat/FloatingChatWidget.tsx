import React, { useState, useEffect } from 'react';
import { ChatPopup } from './ChatPopup';
import { MobileChatPopup } from './MobileChatPopup';
import { ChatIcon } from './ChatIcon';
import { ConnectionDebug } from './ConnectionDebug';
import { useFloatingChat } from '../../hooks/useFloatingChat';
import { useMobileDetection } from '../../hooks/useMobileDetection';

interface FloatingChatWidgetProps {
  enabled?: boolean;
  position?: 'bottom-right' | 'bottom-left';
  autoOpenDelay?: number;
  showWelcomeTooltip?: boolean;
  className?: string;
}

export const FloatingChatWidget: React.FC<FloatingChatWidgetProps> = ({
  enabled = true,
  position = 'bottom-right',
  autoOpenDelay,
  showWelcomeTooltip = true,
  className = '',
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const { isMobile, isTouchDevice } = useMobileDetection();

  const {
    isOpen,
    isMinimized,
    hasNotification,
    toggle,
    close,
    minimize,
    restore,
    setNotification,
  } = useFloatingChat({
    autoOpenDelay,
    persistState: true,
  });

  // Show welcome tooltip after a delay
  useEffect(() => {
    if (showWelcomeTooltip && !isOpen && !isMinimized) {
      const timer = setTimeout(() => {
        setShowTooltip(true);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [showWelcomeTooltip, isOpen, isMinimized]);

  // Hide tooltip when chat is opened
  useEffect(() => {
    if (isOpen) {
      setShowTooltip(false);
    }
  }, [isOpen]);

  // Simulate notification for demo (remove in production)
  useEffect(() => {
    if (!isOpen && !isMinimized) {
      const timer = setTimeout(() => {
        setNotification(true);
      }, 10000); // Show notification after 10 seconds

      return () => clearTimeout(timer);
    }
  }, [isOpen, isMinimized, setNotification]);

  if (!enabled) return null;

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
  };

  const handleIconClick = () => {
    if (isMinimized) {
      restore();
    } else {
      toggle();
    }
    setShowTooltip(false);
  };

  return (
    <div className={`fixed z-50 ${positionClasses[position]} ${className}`}>
      {/* Chat Popup - Mobile or Desktop */}
      {isMobile ? (
        <MobileChatPopup
          isOpen={isOpen}
          onClose={close}
        />
      ) : (
        <ChatPopup
          isOpen={isOpen}
          onClose={close}
          onMinimize={minimize}
        />
      )}

      {/* Chat Icon */}
      <ChatIcon
        isOpen={isOpen}
        hasNotification={hasNotification || isMinimized}
        onClick={handleIconClick}
        onDoubleClick={() => setShowDebug(true)}
        className={isMobile ? 'w-12 h-12' : ''}
      />

      {/* Welcome Tooltip */}
      {showTooltip && !isOpen && !isMinimized && (
        <div className={`
          absolute mb-2 px-4 py-3 bg-white text-gray-800 text-sm rounded-lg shadow-xl border border-gray-200
          max-w-xs transition-all duration-300 ease-in-out
          ${position === 'bottom-right'
            ? 'bottom-16 right-0 transform -translate-x-2'
            : 'bottom-16 left-0 transform translate-x-2'
          }
        `}>
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
            </div>
            <div className="flex-1">
              <p className="font-medium text-gray-900 mb-1">Need help?</p>
              <p className="text-gray-600 text-xs">Chat with our AI assistant for instant support!</p>
            </div>
            <button
              onClick={() => setShowTooltip(false)}
              className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Tooltip Arrow */}
          <div className={`
            absolute w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white
            ${position === 'bottom-right' ? 'top-full right-6' : 'top-full left-6'}
          `}></div>
          <div className={`
            absolute w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200
            ${position === 'bottom-right' ? 'top-full right-6 transform translate-y-px' : 'top-full left-6 transform translate-y-px'}
          `}></div>
        </div>
      )}

      {/* Debug Modal */}
      {showDebug && (
        <ConnectionDebug onClose={() => setShowDebug(false)} />
      )}
    </div>
  );
};
