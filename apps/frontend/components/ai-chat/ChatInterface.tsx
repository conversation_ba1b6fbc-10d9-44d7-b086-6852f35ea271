import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Chat<PERSON>ontainer,
  MessageList,
  Message,
  MessageInput,
  TypingIndicator,
  ConversationHeader,
  Avatar,
} from '@chatscope/chat-ui-kit-react';
import '@chatscope/chat-ui-kit-styles/dist/default/styles.min.css';
import { useChatSocket } from '../../hooks/useChatSocket';
import { ChatMessage } from '../../types/chat.types';

interface ChatInterfaceProps {
  className?: string;
  onClose?: () => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({ className, onClose }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    socket,
    isConnected,
    sendMessage,
    error,
    clearError,
  } = useChatSocket({
    onMessageReceived: (message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
      setIsTyping(false);
    },
    onTypingIndicator: (typing: boolean) => {
      setIsTyping(typing);
    },
    onError: (error: string) => {
      console.error('Chat error:', error);
      setIsTyping(false);
    },
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || !isConnected) return;

    // Add user message to UI immediately
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: message.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      conversationId: conversationId || '',
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Send message via socket
    try {
      const response = await sendMessage({
        message: message.trim(),
        conversationId: conversationId || undefined,
      });

      if (response?.conversationId && !conversationId) {
        setConversationId(response.conversationId);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      setIsTyping(false);
    }
  };

  const handleClearChat = () => {
    setMessages([]);
    setConversationId(null);
    clearError();
  };

  return (
    <div className={`h-full ${className || ''}`}>
      <MainContainer>
        <ChatContainer>
          <ConversationHeader>
            <ConversationHeader.Back onClick={onClose} />
            <Avatar
              src="/ai-avatar.png"
              name="AI Assistant"
              status="available"
            />
            <ConversationHeader.Content
              userName="AI Assistant"
              info={isConnected ? 'Online' : 'Connecting...'}
            />
            <ConversationHeader.Actions>
              <button
                onClick={handleClearChat}
                className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded"
                title="Clear chat"
              >
                Clear
              </button>
            </ConversationHeader.Actions>
          </ConversationHeader>

          <MessageList
            typingIndicator={
              isTyping ? (
                <TypingIndicator content="AI Assistant is typing..." />
              ) : null
            }
          >
            {messages.length === 0 && (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <div className="text-2xl mb-2">🤖</div>
                  <p>Hello! I'm your AI assistant.</p>
                  <p className="text-sm">Ask me anything about nursing or healthcare!</p>
                </div>
              </div>
            )}

            {messages.map((msg) => (
              <Message
                key={msg.id}
                model={{
                  message: msg.message,
                  sentTime: new Date(msg.timestamp).toLocaleTimeString(),
                  sender: msg.sender === 'user' ? 'user' : 'ai',
                  direction: msg.sender === 'user' ? 'outgoing' : 'incoming',
                  position: 'single',
                }}
              >
                {msg.sender === 'ai' && (
                  <Avatar
                    src="/ai-avatar.png"
                    name="AI"
                    size="sm"
                  />
                )}
              </Message>
            ))}
            <div ref={messagesEndRef} />
          </MessageList>

          <MessageInput
            placeholder="Type your message here..."
            onSend={handleSendMessage}
            disabled={!isConnected}
            attachButton={false}
          />

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mx-4 mb-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">{error}</span>
                <button
                  onClick={clearError}
                  className="text-red-700 hover:text-red-900"
                >
                  ×
                </button>
              </div>
            </div>
          )}

          {!isConnected && (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mx-4 mb-4">
              <span className="text-sm">Connecting to chat service...</span>
            </div>
          )}
        </ChatContainer>
      </MainContainer>
    </div>
  );
};
