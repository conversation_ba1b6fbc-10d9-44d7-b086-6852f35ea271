import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Chat<PERSON>ontainer,
  MessageList,
  Message,
  MessageInput,
  TypingIndicator,
  ConversationHeader,
  Avatar,
} from '@chatscope/chat-ui-kit-react';
import { useChatSocket } from '../../hooks/useChatSocket';
import { ChatMessage } from '../../types/chat.types';

interface ChatPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize?: () => void;
  className?: string;
}

export const ChatPopup: React.FC<ChatPopupProps> = ({
  isOpen,
  onClose,
  onMinimize,
  className = '',
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    socket,
    isConnected,
    sendMessage,
    error,
    clearError,
  } = useChatSocket({
    onMessageReceived: (message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
      setIsTyping(false);
    },
    onTypingIndicator: (typing: boolean) => {
      setIsTyping(typing);
    },
    onError: (error: string) => {
      console.error('Chat error:', error);
      setIsTyping(false);
    },
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || !isConnected) return;

    // Add user message to UI immediately
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: message.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      conversationId: conversationId || '',
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Send message via socket
    try {
      const response = await sendMessage({
        message: message.trim(),
        conversationId: conversationId || undefined,
      });

      if (response?.conversationId && !conversationId) {
        setConversationId(response.conversationId);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      setIsTyping(false);
    }
  };

  const handleClearChat = () => {
    setMessages([]);
    setConversationId(null);
    clearError();
  };

  if (!isOpen) return null;

  return (
    <div className={`
      fixed bottom-20 right-4 z-50
      w-96 h-[500px] max-h-[80vh]
      bg-white rounded-2xl shadow-2xl border border-gray-200
      transform transition-all duration-300 ease-in-out
      ${isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0 pointer-events-none'}
      ${className}
    `}>
      {/* Custom Header */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-2xl">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <Avatar
              src="/ai-avatar.png"
              name="AI Assistant"
              size="sm"
              className="border-2 border-white"
            />
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border border-white"></div>
          </div>
          <div>
            <h3 className="font-semibold text-sm">AI Assistant</h3>
            <p className="text-xs opacity-90">
              {isConnected ? 'Online' : 'Connecting...'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {onMinimize && (
            <button
              onClick={onMinimize}
              className="p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
              title="Minimize"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </button>
          )}
          
          <button
            onClick={handleClearChat}
            className="p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
            title="Clear chat"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
          
          <button
            onClick={onClose}
            className="p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
            title="Close"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Chat Content */}
      <div className="h-[calc(100%-80px)]">
        <MainContainer>
          <ChatContainer>
            <MessageList
              typingIndicator={
                isTyping ? (
                  <TypingIndicator content="AI Assistant is typing..." />
                ) : null
              }
              className="p-4"
            >
              {messages.length === 0 && (
                <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
                  <div className="text-4xl mb-3">🤖</div>
                  <h4 className="font-semibold text-gray-700 mb-2">Hello! I'm your AI assistant</h4>
                  <p className="text-sm text-center text-gray-600 mb-4">
                    I can help you with nursing questions, platform guidance, and healthcare information.
                  </p>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>Try asking:</p>
                    <p>• "How do I register as a nurse?"</p>
                    <p>• "What services are available?"</p>
                    <p>• "How does payment work?"</p>
                  </div>
                </div>
              )}

              {messages.map((msg) => (
                <Message
                  key={msg.id}
                  model={{
                    message: msg.message,
                    sentTime: new Date(msg.timestamp).toLocaleTimeString(),
                    sender: msg.sender === 'user' ? 'user' : 'ai',
                    direction: msg.sender === 'user' ? 'outgoing' : 'incoming',
                    position: 'single',
                  }}
                >
                  {msg.sender === 'ai' && (
                    <Avatar
                      src="/ai-avatar.png"
                      name="AI"
                      size="sm"
                    />
                  )}
                </Message>
              ))}
              <div ref={messagesEndRef} />
            </MessageList>

            <MessageInput
              placeholder="Type your message..."
              onSend={handleSendMessage}
              disabled={!isConnected}
              attachButton={false}
              className="border-t"
            />
          </ChatContainer>
        </MainContainer>
      </div>

      {/* Error Message */}
      {error && (
        <div className="absolute bottom-16 left-4 right-4 bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded-lg text-sm">
          <div className="flex justify-between items-center">
            <span>{error}</span>
            <button
              onClick={clearError}
              className="text-red-700 hover:text-red-900 ml-2"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Connection Status */}
      {!isConnected && (
        <div className="absolute bottom-16 left-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-3 py-2 rounded-lg text-sm">
          <span>Connecting to chat service...</span>
        </div>
      )}
    </div>
  );
};
