import React from 'react';

interface ChatIconProps {
  isOpen: boolean;
  hasNotification?: boolean;
  onClick: () => void;
  onDoubleClick?: () => void;
  className?: string;
}

export const ChatIcon: React.FC<ChatIconProps> = ({
  isOpen,
  hasNotification = false,
  onClick,
  onDoubleClick,
  className = '',
}) => {
  return (
    <button
      onClick={onClick}
      onDoubleClick={onDoubleClick}
      className={`
        relative group
        w-14 h-14 rounded-full shadow-lg
        transition-all duration-300 ease-in-out
        transform hover:scale-110 active:scale-95
        focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50
        ${isOpen 
          ? 'bg-red-500 hover:bg-red-600 rotate-180' 
          : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
        }
        text-white flex items-center justify-center
        ${className}
      `}
      aria-label={isOpen ? 'Close chat' : 'Open chat'}
    >
      {/* Notification Badge */}
      {hasNotification && !isOpen && (
        <div className="absolute -top-1 -right-1 w-4 h-4">
          <div className="w-full h-full bg-red-500 rounded-full border-2 border-white animate-pulse"></div>
          <div className="absolute inset-0 w-full h-full bg-red-500 rounded-full animate-ping"></div>
        </div>
      )}

      {/* Icon */}
      <div className="relative">
        {isOpen ? (
          // Close Icon
          <svg
            className="w-6 h-6 transition-transform duration-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        ) : (
          // Chat Icon
          <div className="relative">
            <svg
              className="w-6 h-6 transition-transform duration-300 group-hover:scale-110"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
            
            {/* AI Indicator */}
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border border-white">
              <div className="w-full h-full bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        )}
      </div>

      {/* Ripple Effect */}
      <div className="absolute inset-0 rounded-full bg-white opacity-0 group-active:opacity-20 transition-opacity duration-150"></div>
    </button>
  );
};
