import React from "react";
import { UserSearch, UserPlus } from "lucide-react";

export default function CallToAction() {
  return (
    <section
      className="w-full bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 text-white text-center py-20 px-4 sm:px-8"
    >
      <div className="max-w-5xl mx-auto">
        <h2
          className="text-4xl font-extrabold mb-4 tracking-tight"
        >
          Ready to Experience Better Home Healthcare?
        </h2>
        <p
          className="mb-10 text-lg max-w-2xl mx-auto"
        >
          Join thousands of patients and nurses who trust NurseConnect for professional, convenient home healthcare services.
        </p>
        <div
          className="flex justify-center flex-wrap gap-4"
        >
          <button className="flex items-center gap-2 bg-white text-blue-600 font-semibold px-6 py-3 rounded-full hover:bg-blue-100 transition-all duration-300 shadow-md">
            <UserSearch size={20} />
            Find a Nurse
          </button>
          <button className="flex items-center gap-2 bg-blue-500 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-full border border-white transition-all duration-300 shadow-md">
            <UserPlus size={20} />
            Register as a Nurse
          </button>
        </div>
      </div>
    </section>
  );
}
