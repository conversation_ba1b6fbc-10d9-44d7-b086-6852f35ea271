#!/bin/bash

# AI Chat System Startup Script
echo "🚀 Starting AI Chat System..."
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Function to wait for a service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    echo -e "${YELLOW}Waiting for $service_name to be ready...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready!${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}⏳ Attempt $attempt/$max_attempts - waiting for $service_name...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to start after $max_attempts attempts${NC}"
    return 1
}

# Check if required directories exist
if [ ! -d "apps/backend" ] || [ ! -d "apps/frontend" ]; then
    echo -e "${RED}❌ Error: apps/backend or apps/frontend directory not found${NC}"
    echo "Please run this script from the project root directory"
    exit 1
fi

# Check if node_modules exist
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}⚠️  node_modules not found. Installing dependencies...${NC}"
    npm install
fi

# Check backend dependencies
if [ ! -d "apps/backend/node_modules" ]; then
    echo -e "${YELLOW}⚠️  Backend dependencies not found. Installing...${NC}"
    cd apps/backend && npm install && cd ../..
fi

# Check frontend dependencies
if [ ! -d "apps/frontend/node_modules" ]; then
    echo -e "${YELLOW}⚠️  Frontend dependencies not found. Installing...${NC}"
    cd apps/frontend && npm install && cd ../..
fi

# Check if backend is already running
if check_port 3001; then
    echo -e "${YELLOW}⚠️  Backend already running on port 3001${NC}"
    read -p "Kill existing backend process? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🔄 Killing existing backend process...${NC}"
        pkill -f "nest start" || true
        pkill -f "node.*backend" || true
        sleep 2
    fi
fi

# Check if frontend is already running
if check_port 3000; then
    echo -e "${YELLOW}⚠️  Frontend already running on port 3000${NC}"
    read -p "Kill existing frontend process? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🔄 Killing existing frontend process...${NC}"
        pkill -f "next dev" || true
        pkill -f "node.*frontend" || true
        sleep 2
    fi
fi

# Start backend
echo -e "${BLUE}🔧 Starting backend server...${NC}"
cd apps/backend
npm run start:dev > ../../backend.log 2>&1 &
BACKEND_PID=$!
cd ../..

# Wait for backend to be ready
if wait_for_service "http://localhost:3001/ai-chat/health" "Backend"; then
    echo -e "${GREEN}✅ Backend started successfully (PID: $BACKEND_PID)${NC}"
else
    echo -e "${RED}❌ Backend failed to start${NC}"
    echo "Check backend.log for errors:"
    tail -20 backend.log
    exit 1
fi

# Test backend AI chat endpoints
echo -e "${BLUE}🧪 Testing backend endpoints...${NC}"
if node test-ai-chat.js > test-results.log 2>&1; then
    echo -e "${GREEN}✅ Backend tests passed${NC}"
else
    echo -e "${YELLOW}⚠️  Some backend tests failed. Check test-results.log${NC}"
fi

# Start frontend
echo -e "${BLUE}🎨 Starting frontend server...${NC}"
cd apps/frontend
npm run dev > ../../frontend.log 2>&1 &
FRONTEND_PID=$!
cd ../..

# Wait for frontend to be ready
if wait_for_service "http://localhost:3000" "Frontend"; then
    echo -e "${GREEN}✅ Frontend started successfully (PID: $FRONTEND_PID)${NC}"
else
    echo -e "${RED}❌ Frontend failed to start${NC}"
    echo "Check frontend.log for errors:"
    tail -20 frontend.log
    exit 1
fi

# Success message
echo ""
echo -e "${GREEN}🎉 AI Chat System is now running!${NC}"
echo "=================================="
echo -e "${BLUE}📱 Frontend:${NC} http://localhost:3000"
echo -e "${BLUE}🔧 Backend:${NC} http://localhost:3001"
echo -e "${BLUE}🧪 Chat Test:${NC} http://localhost:3000/chat-test"
echo -e "${BLUE}💬 Chat Page:${NC} http://localhost:3000/chat"
echo ""
echo -e "${YELLOW}📋 Process IDs:${NC}"
echo "Backend PID: $BACKEND_PID"
echo "Frontend PID: $FRONTEND_PID"
echo ""
echo -e "${YELLOW}📝 Log files:${NC}"
echo "Backend logs: backend.log"
echo "Frontend logs: frontend.log"
echo "Test results: test-results.log"
echo ""
echo -e "${YELLOW}🛑 To stop services:${NC}"
echo "kill $BACKEND_PID $FRONTEND_PID"
echo "or run: pkill -f 'nest start|next dev'"
echo ""
echo -e "${GREEN}✨ Happy chatting!${NC}"

# Keep script running to show logs
echo -e "${BLUE}📊 Monitoring services (Ctrl+C to exit)...${NC}"
echo "Backend and frontend are running in the background."
echo "Visit http://localhost:3000/chat-test to test the chat widget."

# Wait for user to exit
trap 'echo -e "\n${YELLOW}🛑 Stopping services...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# Show live logs
while true; do
    sleep 5
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo -e "${RED}❌ Backend process died!${NC}"
        tail -10 backend.log
        break
    fi
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo -e "${RED}❌ Frontend process died!${NC}"
        tail -10 frontend.log
        break
    fi
done
