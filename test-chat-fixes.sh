#!/bin/bash

# Test script to verify chat fixes
echo "🧪 Testing Chat Widget Fixes..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if backend is running
print_status "Checking backend status..."
if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    print_success "Backend is running on port 3001"
else
    print_error "Backend is not running on port 3001"
    echo "Please start the backend first:"
    echo "  cd apps/backend && npm run start:dev"
    exit 1
fi

# Check if frontend is running
print_status "Checking frontend status..."
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    print_success "Frontend is running on port 3000"
else
    print_warning "Frontend is not running on port 3000"
    echo "To start frontend:"
    echo "  cd apps/frontend && npm run dev"
fi

# Test WebSocket connection
print_status "Testing WebSocket connection..."
if command -v node &> /dev/null; then
    timeout 10s node test-websocket-connection.js
    if [ $? -eq 0 ]; then
        print_success "WebSocket connection test passed"
    else
        print_warning "WebSocket connection test failed or timed out"
        echo "This might be normal if OpenAI API key is not configured"
    fi
else
    print_warning "Node.js not found, skipping WebSocket test"
fi

# Check environment files
print_status "Checking environment configuration..."

if [ -f "apps/backend/.env" ]; then
    if grep -q "OPENAI_API_KEY=" apps/backend/.env; then
        if grep -q "OPENAI_API_KEY=your_openai_api_key_here" apps/backend/.env; then
            print_warning "OpenAI API key is not configured (using placeholder)"
            echo "Please update OPENAI_API_KEY in apps/backend/.env"
        else
            print_success "OpenAI API key is configured"
        fi
    else
        print_warning "OpenAI API key not found in backend .env"
    fi
else
    print_error "Backend .env file not found"
fi

if [ -f "apps/frontend/.env.local" ]; then
    print_success "Frontend environment file exists"
else
    print_warning "Frontend .env.local file not found"
fi

# Test fixes summary
echo ""
echo "🔧 Fixes Applied:"
echo "  ✅ Fixed infinite re-render in useChatSocket hook"
echo "  ✅ Improved WebSocket connection handling"
echo "  ✅ Added connection retry logic"
echo "  ✅ Enhanced error handling and logging"
echo "  ✅ Added debug mode (double-click chat icon)"
echo "  ✅ Chat widget now appears on all pages"
echo ""

echo "🧪 How to Test:"
echo "  1. Visit: http://localhost:3000"
echo "  2. Look for the blue chat icon in bottom-right corner"
echo "  3. Click the icon to open chat"
echo "  4. Double-click the icon to open debug mode"
echo "  5. Try sending a message like 'Hello'"
echo ""

echo "🐛 If Issues Persist:"
echo "  1. Check browser console for errors"
echo "  2. Use debug mode (double-click chat icon)"
echo "  3. Verify OpenAI API key is set"
echo "  4. Restart both backend and frontend"
echo ""

print_success "Test completed!"
