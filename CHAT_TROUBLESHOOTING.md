# 🔧 AI Chat Troubleshooting Guide

## 🚀 Quick Start

### Option 1: Automated Startup (Recommended)
```bash
# Run the automated startup script
./start-ai-chat.sh
```

### Option 2: Manual Startup
```bash
# Terminal 1 - Backend
cd apps/backend
npm run start:dev

# Terminal 2 - Frontend  
cd apps/frontend
npm run dev

# Terminal 3 - Test
node test-ai-chat.js
```

## 🔍 Common Issues & Solutions

### 1. Chat Widget Not Appearing

**Symptoms:**
- No floating chat icon visible
- No errors in console

**Solutions:**
1. **Check Integration:**
   ```bash
   # Visit the test page
   http://localhost:3000/chat-test
   ```

2. **Verify CSS Import:**
   - Check if `floating-chat-animations.css` is imported in `_app.tsx`
   - Look for import: `import '../styles/floating-chat-animations.css';`

3. **Check Component Import:**
   - Verify `FloatingChatWidget` is imported in `Layout.tsx`
   - Check if `<FloatingChatWidget />` is added to Layout

4. **Browser Console Check:**
   ```javascript
   // Open browser console and check for errors
   console.log('FloatingChatWidget loaded:', !!window.FloatingChatWidget);
   ```

### 2. Chat Widget Appears But Doesn't Open

**Symptoms:**
- Chat icon is visible
- Clicking doesn't open chat popup
- No console errors

**Solutions:**
1. **Check Dependencies:**
   ```bash
   # Verify required packages are installed
   npm list @chatscope/chat-ui-kit-react socket.io-client
   ```

2. **Check CSS Conflicts:**
   - Look for z-index conflicts
   - Check if popup is hidden behind other elements
   - Try adding higher z-index to chat popup

3. **Debug State:**
   ```javascript
   // In browser console
   localStorage.getItem('floating-chat-state');
   ```

### 3. Chat Opens But No Connection

**Symptoms:**
- Chat popup opens
- Shows "Connecting..." status
- Never connects to backend

**Solutions:**
1. **Check Backend Status:**
   ```bash
   curl http://localhost:3001/ai-chat/health
   ```

2. **Verify Environment Variables:**
   ```bash
   # Check frontend .env.local
   cat apps/frontend/.env.local
   # Should contain: NEXT_PUBLIC_API_URL=http://localhost:3001
   ```

3. **Check WebSocket Connection:**
   ```javascript
   // In browser console
   const socket = io('http://localhost:3001/ai-chat');
   socket.on('connect', () => console.log('Connected!'));
   ```

### 4. Messages Send But No Response

**Symptoms:**
- Can send messages
- Messages appear in chat
- No AI responses received

**Solutions:**
1. **Check OpenAI API Key:**
   ```bash
   # Verify backend .env has valid OpenAI key
   grep OPENAI_API_KEY apps/backend/.env
   ```

2. **Test Knowledge Base:**
   ```bash
   curl "http://localhost:3001/ai-chat/knowledge-base/search?q=register"
   ```

3. **Check Backend Logs:**
   ```bash
   # Look for errors in backend console
   tail -f backend.log
   ```

### 5. Mobile Chat Issues

**Symptoms:**
- Chat works on desktop but not mobile
- Mobile chat doesn't open full-screen
- Touch gestures not working

**Solutions:**
1. **Check Viewport Meta Tag:**
   ```html
   <!-- Should be in pages/_app.tsx or _document.tsx -->
   <meta name="viewport" content="width=device-width, initial-scale=1" />
   ```

2. **Test Mobile Detection:**
   ```javascript
   // In browser console on mobile
   window.innerWidth < 768 // Should be true on mobile
   ```

3. **Check Touch Events:**
   ```javascript
   // Test touch support
   'ontouchstart' in window // Should be true on touch devices
   ```

## 🧪 Testing Steps

### 1. Basic Functionality Test
1. Visit `http://localhost:3000/chat-test`
2. Check debug info shows all green checkmarks
3. Look for chat icon in bottom-right corner
4. Click icon to open chat
5. Send message: "How do I register as a nurse?"
6. Verify you get a knowledge base response

### 2. Hybrid AI Test
Try these messages to test different response types:

**Knowledge Base Response (should be instant):**
- "How do I register as a nurse?"
- "What documents do I need?"
- "How does payment work?"

**Hybrid Response (KB + OpenAI):**
- "Tell me about the registration process"
- "What are the payment options?"

**Pure OpenAI Response:**
- "What's the weather like?"
- "Tell me a joke"
- "How can I improve my nursing skills?"

### 3. Mobile Responsiveness Test
1. Open browser dev tools
2. Switch to mobile view (iPhone/Android)
3. Refresh page
4. Test chat functionality
5. Verify full-screen mobile chat opens

## 📊 Debug Information

### Check Service Status
```bash
# Backend health check
curl http://localhost:3001/ai-chat/health

# Knowledge base stats
curl http://localhost:3001/ai-chat/knowledge-base/stats

# Test search
curl "http://localhost:3001/ai-chat/knowledge-base/search?q=register"
```

### Browser Console Commands
```javascript
// Check if components are loaded
console.log('React:', typeof React);
console.log('Socket.IO:', typeof io);

// Check environment variables
console.log('API URL:', process.env.NEXT_PUBLIC_API_URL);

// Check local storage
console.log('Chat state:', localStorage.getItem('floating-chat-state'));

// Force chat to open
document.querySelector('[aria-label="Open chat"]')?.click();
```

### Log Files
```bash
# Backend logs
tail -f backend.log

# Frontend logs  
tail -f frontend.log

# Test results
cat test-results.log
```

## 🔧 Manual Fixes

### Reset Chat State
```javascript
// Clear chat state in browser console
localStorage.removeItem('floating-chat-state');
localStorage.removeItem('ai-chat-conversations');
location.reload();
```

### Force CSS Reload
```bash
# Clear Next.js cache
rm -rf apps/frontend/.next
cd apps/frontend && npm run dev
```

### Restart Services
```bash
# Kill all processes
pkill -f "nest start"
pkill -f "next dev"

# Restart
./start-ai-chat.sh
```

## 📞 Getting Help

If you're still having issues:

1. **Check the debug page:** `http://localhost:3000/chat-test`
2. **Review browser console** for JavaScript errors
3. **Check network tab** for failed requests
4. **Verify all files exist** as listed in the file structure
5. **Test with a fresh browser session** (incognito mode)

## 🎯 Expected Behavior

When everything is working correctly:

✅ Chat icon appears in bottom-right corner  
✅ Icon has blue gradient background with chat bubble icon  
✅ Clicking opens chat popup (desktop) or full-screen (mobile)  
✅ "How do I register?" gets instant knowledge base response  
✅ Other questions get OpenAI responses  
✅ Typing indicators show during AI responses  
✅ Chat history persists in local storage  
✅ Mobile version opens full-screen with swipe gestures  

If any of these don't work, follow the troubleshooting steps above!
